// 初始化收入图表
function initIncomeCharts() {
  try {
    // 确保Canvas元素存在并可用
    function getCanvasContext(id) {
      const canvas = document.getElementById(id);
      if (!canvas) {
        throw new Error(`找不到Canvas元素: ${id}`);
      }
      return canvas.getContext('2d');
    }
    
    const ctxDaily = getCanvasContext('incomeDailyChart');
    const ctxProduct = getCanvasContext('incomeProductChart');
    
    // 销毁之前的图表实例
    if (StatisticsState.charts.incomeDailyChart) StatisticsState.charts.incomeDailyChart.destroy();
    if (StatisticsState.charts.incomeProductChart) StatisticsState.charts.incomeProductChart.destroy();
    
    // 创建每日收入趋势图表
    StatisticsState.charts.incomeDailyChart = new Chart(ctxDaily, {
      type: 'line',
      data: {
        labels: [],
        datasets: [
          {
            label: '总收入',
            data: [],
            borderColor: '#2ecc71',
            backgroundColor: 'rgba(46, 204, 113, 0.1)',
            borderWidth: 2,
            tension: 0.1,
            fill: true
          },
          {
            label: '微信支付',
            data: [],
            borderColor: '#27ae60',
            backgroundColor: 'rgba(39, 174, 96, 0.1)',
            borderWidth: 2,
            tension: 0.1,
            fill: true
          },
          {
            label: '支付宝',
            data: [],
            borderColor: '#3498db',
            backgroundColor: 'rgba(52, 152, 219, 0.1)',
            borderWidth: 2,
            tension: 0.1,
            fill: true
          },
          {
            label: 'PayPal',
            data: [],
            borderColor: '#9b59b6',
            backgroundColor: 'rgba(155, 89, 182, 0.1)',
            borderWidth: 2,
            tension: 0.1,
            fill: true
          }
        ]
      },
      options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
          title: {
            display: false
          },
          tooltip: {
            mode: 'index',
            intersect: false,
            callbacks: {
              label: function(context) {
                let label = context.dataset.label || '';
                if (label) {
                  label += ': ';
                }
                if (context.parsed.y !== null) {
                  label += '¥' + context.parsed.y.toFixed(2);
                }
                return label;
              }
            }
          }
        },
        scales: {
          x: {
            grid: {
              display: false
            }
          },
          y: {
            beginAtZero: true,
            ticks: {
              callback: function(value) {
                return '¥' + value.toFixed(2);
              }
            }
          }
        }
      }
    });
    
    // 创建产品收入分布图表
    StatisticsState.charts.incomeProductChart = new Chart(ctxProduct, {
      type: 'pie',
      data: {
        labels: [],
        datasets: [{
          data: [],
          backgroundColor: [
            'rgba(46, 204, 113, 0.7)',
            'rgba(52, 152, 219, 0.7)',
            'rgba(155, 89, 182, 0.7)',
            'rgba(230, 126, 34, 0.7)',
            'rgba(231, 76, 60, 0.7)',
            'rgba(241, 196, 15, 0.7)',
            'rgba(149, 165, 166, 0.7)'
          ],
          borderColor: [
            'rgba(46, 204, 113, 1)',
            'rgba(52, 152, 219, 1)',
            'rgba(155, 89, 182, 1)',
            'rgba(230, 126, 34, 1)',
            'rgba(231, 76, 60, 1)',
            'rgba(241, 196, 15, 1)',
            'rgba(149, 165, 166, 1)'
          ],
          borderWidth: 1
        }]
      },
      options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
          tooltip: {
            callbacks: {
              label: function(context) {
                const label = context.label || '';
                const value = context.raw || 0;
                const total = context.dataset.data.reduce((a, b) => a + b, 0);
                const percentage = total > 0 ? Math.round((value / total) * 100) : 0;
                return `${label}: ¥${value.toFixed(2)} (${percentage}%)`;
              }
            }
          }
        }
      }
    });
    
    StatisticsState.flags.incomeChartsInitialized = true;
  } catch (error) {
    console.error('初始化收入图表出错:', error);
    showError('初始化收入图表失败: ' + error.message);
  }
}

// 更新收入图表数据
function updateIncomeCharts() {
  if (!StatisticsState.data.incomeData || !StatisticsState.flags.incomeChartsInitialized) {
    console.warn('无法更新收入图表：数据不完整或图表未初始化');
    return;
  }
  
  try {
    const { 
      dateRange, 
      totalIncomeData, 
      wechatIncomeData, 
      alipayIncomeData,
      paypalIncomeData,
      productDistribution
    } = StatisticsState.data.incomeData;
    
    // 检查数据完整性
    if (!dateRange || !dateRange.length) {
      console.warn('日期范围数据不完整');
      return;
    }
    
    // 更新每日收入趋势图表
    StatisticsState.charts.incomeDailyChart.data.labels = dateRange;
    StatisticsState.charts.incomeDailyChart.data.datasets[0].data = dateRange.map(date => totalIncomeData[date] || 0);
    StatisticsState.charts.incomeDailyChart.data.datasets[1].data = dateRange.map(date => wechatIncomeData[date] || 0);
    StatisticsState.charts.incomeDailyChart.data.datasets[2].data = dateRange.map(date => alipayIncomeData[date] || 0);
    StatisticsState.charts.incomeDailyChart.data.datasets[3].data = dateRange.map(date => paypalIncomeData[date] || 0);
    StatisticsState.charts.incomeDailyChart.update();
    
    // 更新产品收入分布图表
    if (productDistribution && productDistribution.length > 0) {
      // 提取产品名称和金额
      const productLabels = productDistribution.map(item => item.product_name);
      const productAmounts = productDistribution.map(item => parseFloat(item.total_amount) || 0);
      
      // 更新图表数据
      StatisticsState.charts.incomeProductChart.data.labels = productLabels;
      StatisticsState.charts.incomeProductChart.data.datasets[0].data = productAmounts;
      StatisticsState.charts.incomeProductChart.update();
    } else {
      // 如果没有产品数据，显示空图表
      StatisticsState.charts.incomeProductChart.data.labels = ['暂无数据'];
      StatisticsState.charts.incomeProductChart.data.datasets[0].data = [1];
      StatisticsState.charts.incomeProductChart.update();
    }
  } catch (error) {
    console.error('更新收入图表出错:', error);
    showError('更新收入图表失败: ' + error.message);
  }
}

// 更新收入统计摘要
function updateIncomeSummary() {
  if (!StatisticsState.data.incomeData) return;
  
  try {
    const { summary } = StatisticsState.data.incomeData;
    
    // 更新统计显示
    $('#total-income').text('¥' + (summary.totalIncome || 0).toFixed(2));
    $('#wechat-income').text('¥' + (summary.wechatIncome || 0).toFixed(2));
    $('#alipay-income').text('¥' + (summary.alipayIncome || 0).toFixed(2));
    $('#paypal-income').text('¥' + (summary.paypalIncome || 0).toFixed(2));
    $('#order-count').text(summary.orderCount || 0);
    
  } catch (error) {
    console.error('更新收入统计摘要出错:', error);
    showError('更新收入统计摘要失败: ' + error.message);
  }
}

// 获取收入统计数据
function fetchIncomeStats() {
  const dateRange = $('#daterange').val().split(' - ');
  const startDate = dateRange[0];
  const endDate = dateRange[1];
  
  axios.get('/api/income-stats', {
    params: {
      startDate,
      endDate
    }
  })
  .then(response => {
    if (response.data.success) {
      StatisticsState.data.incomeData = response.data.data;
      
      // 初始化收入图表
      initIncomeCharts();
      
      // 更新收入图表数据
      updateIncomeCharts();
      
      // 更新收入统计摘要
      updateIncomeSummary();
    } else {
      showError('获取收入数据失败: ' + response.data.message);
    }
  })
  .catch(error => {
    console.error('获取收入数据失败:', error);
    showError('获取收入数据失败: ' + (error.response?.data?.message || error.message));
  });
} 