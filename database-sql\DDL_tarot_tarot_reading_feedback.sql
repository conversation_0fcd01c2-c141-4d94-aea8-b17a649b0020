/******************************************/
/*   DatabaseName = tarot   */
/*   TableName = reading_feedback   */
/******************************************/
CREATE TABLE `reading_feedback` (
  `id` varchar(36) COLLATE utf8mb4_unicode_ci NOT NULL,
  `user_id` varchar(36) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `session_id` varchar(36) COLLATE utf8mb4_unicode_ci NOT NULL,
  `basic_reading_rating` tinyint DEFAULT NULL,
  `basic_reading_content` text COLLATE utf8mb4_unicode_ci,
  `deep_analysis_rating` tinyint DEFAULT NULL,
  `deep_analysis_content` text COLLATE utf8mb4_unicode_ci,
  `followup_rating` tinyint DEFAULT NULL,
  `followup_content` text COLLATE utf8mb4_unicode_ci,
  `metadata` json DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_session_id` (`session_id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
;
