/******************************************/
/*   DatabaseName = tarot   */
/*   TableName = user_feedback   */
/******************************************/
CREATE TABLE `user_feedback` (
  `id` varchar(36) NOT NULL,
  `user_id` varchar(36) DEFAULT NULL,
  `content` text NOT NULL,
  `type` enum('problem','suggestion','other') NOT NULL DEFAULT 'other',
  `email` varchar(255) DEFAULT NULL,
  `username` varchar(255) DEFAULT NULL,
  `metadata` json DEFAULT NULL,
  `created_at` datetime NOT NULL,
  `updated_at` datetime NOT NULL,
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_type` (`type`),
  <PERSON>EY `idx_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3
;
