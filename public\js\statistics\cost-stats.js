// 价格常量配置
const PRICES = {
  MODEL_INPUT: 0.0024, // 模型调用-输入：¥0.0024 / 千Token (非日运会话)，2025年6月25日前
  MODEL_OUTPUT: 0.0096,  // 模型调用-输出：¥0.0096 / 千Token (非日运会话)，2025年6月25日前
  MODEL_INPUT_NEW: 0.0008, // 模型调用-输入：¥0.0008 / 千Token (非日运会话)，2025年6月25日起至6月30日前
  MODEL_OUTPUT_NEW: 0.002, // 模型调用-输出：¥0.002 / 千Token (非日运会话)，2025年6月25日起至6月30日前
  MODEL_INPUT_JUNE30: 0.0024, // 模型调用-输入：¥0.0024 / 千Token (非日运会话)，2025年6月30日起
  MODEL_OUTPUT_JUNE30: 0.0096, // 模型调用-输出：¥0.0096 / 千Token (非日运会话)，2025年6月30日起
  MODEL_INPUT_JULY10: 0.0008, // 模型调用-输入：¥0.0008 / 千Token (非日运会话)，2025年7月10日起
  MODEL_OUTPUT_JULY10: 0.002, // 模型调用-输出：¥0.002 / 千Token (非日运会话)，2025年7月10日起
  DAILY_FORTUNE_INPUT: 0.0003, // 日运会话-输入：¥0.0003 / 千Token
  DAILY_FORTUNE_OUTPUT: 0.0006, // 日运会话-输出：¥0.0006 / 千Token
  ETHICAL_INPUT: 0.0003, // 伦理检查-输入：¥0.0003 / 千Token
  ETHICAL_OUTPUT: 0.0006, // 伦理检查-输出：¥0.0006 / 千Token
  SPREAD_RECOM_INPUT: 0.0003, // AI推荐牌阵-输入：¥0.0003 / 千Token（与安全检测一致）
  SPREAD_RECOM_OUTPUT: 0.0006, // AI推荐牌阵-输出：¥0.0006 / 千Token（与安全检测一致）
  HOROSCOPE_INPUT: 0.0008, // 星座运势-输入：¥0.0008 / 千Token
  HOROSCOPE_OUTPUT: 0.002, // 星座运势-输出：¥0.002 / 千Token
  ANONYMOUS_INPUT: 0.0008, // 未登录用户-输入：¥0.0008 / 千Token
  ANONYMOUS_OUTPUT: 0.002, // 未登录用户-输出：¥0.002 / 千Token
  TTS: 0.0005 // TTS语音：¥0.0005 / 千字符
};

// 判断日期是否在2025年6月25日之后
function isAfterPriceChange(dateStr) {
  const priceChangeDate = new Date('2025-06-25');
  const date = new Date(dateStr);
  return date >= priceChangeDate;
}

// 判断日期是否在2025年6月30日之后
function isAfterJune30(dateStr) {
  const june30Date = new Date('2025-06-30');
  const date = new Date(dateStr);
  return date >= june30Date;
}

// 判断日期是否在2025年7月10日之后
function isAfterJuly10(dateStr) {
  const july10Date = new Date('2025-07-10');
  const date = new Date(dateStr);
  return date >= july10Date;
}

// 根据日期获取相应的价格
function getModelInputPrice(dateStr) {
  if (isAfterJuly10(dateStr)) {
    return PRICES.MODEL_INPUT_JULY10;
  } else if (isAfterJune30(dateStr)) {
    return PRICES.MODEL_INPUT_JUNE30;
  }
  return isAfterPriceChange(dateStr) ? PRICES.MODEL_INPUT_NEW : PRICES.MODEL_INPUT;
}

function getModelOutputPrice(dateStr) {
  if (isAfterJuly10(dateStr)) {
    return PRICES.MODEL_OUTPUT_JULY10;
  } else if (isAfterJune30(dateStr)) {
    return PRICES.MODEL_OUTPUT_JUNE30;
  }
  return isAfterPriceChange(dateStr) ? PRICES.MODEL_OUTPUT_NEW : PRICES.MODEL_OUTPUT;
}

// 初始化成本图表
function initCostCharts() {
  try {
    // 确保Canvas元素存在并可用
    function getCanvasContext(id) {
      const canvas = document.getElementById(id);
      if (!canvas) {
        throw new Error(`找不到Canvas元素: ${id}`);
      }
      return canvas.getContext('2d');
    }
    
    const ctxDaily = getCanvasContext('costDailyChart');
    const ctxSpreadRecomTime = getCanvasContext('spreadRecomTimeChart'); // 新增：推荐牌阵处理时间图表
    
    // 销毁之前的图表实例
    if (StatisticsState.charts.costDailyChart) StatisticsState.charts.costDailyChart.destroy();
    if (StatisticsState.charts.spreadRecomTimeChart) StatisticsState.charts.spreadRecomTimeChart.destroy(); // 新增：销毁旧图表
    
    // 创建每日成本趋势图表
    StatisticsState.charts.costDailyChart = new Chart(ctxDaily, {
      type: 'line',
      data: {
        labels: [],
        datasets: [
          {
            label: '总成本',
            data: [],
            borderColor: '#e74c3c',
            backgroundColor: 'rgba(231, 76, 60, 0.1)',
            borderWidth: 2,
            tension: 0.1,
            fill: true
          },
          {
            label: '模型调用成本',
            data: [],
            borderColor: '#3498db',
            backgroundColor: 'rgba(52, 152, 219, 0.1)',
            borderWidth: 2,
            tension: 0.1,
            fill: true
          },
          {
            label: '日运占卜成本',
            data: [],
            borderColor: '#a55eea',
            backgroundColor: 'rgba(165, 94, 234, 0.1)',
            borderWidth: 2,
            tension: 0.1,
            fill: true
          },
          // {
          //   label: '伦理检查成本',
          //   data: [],
          //   borderColor: '#9b59b6',
          //   backgroundColor: 'rgba(155, 89, 182, 0.1)',
          //   borderWidth: 2,
          //   tension: 0.1,
          //   fill: true
          // },
          {
            label: '未登录用户模型调用成本',
            data: [],
            borderColor: '#9b59b6',
            backgroundColor: 'rgba(155, 89, 182, 0.1)',
            borderWidth: 2,
            tension: 0.1,
            fill: true
          },
          {
            label: 'AI推荐牌阵成本',
            data: [],
            borderColor: '#e84393',
            backgroundColor: 'rgba(232, 67, 147, 0.1)',
            borderWidth: 2,
            tension: 0.1,
            fill: true
          },
          {
            label: 'TTS语音成本',
            data: [],
            borderColor: '#2ecc71',
            backgroundColor: 'rgba(46, 204, 113, 0.1)',
            borderWidth: 2,
            tension: 0.1,
            fill: true
          },
          {
            label: '星座运势成本',
            data: [],
            borderColor: '#f39c12',
            backgroundColor: 'rgba(243, 156, 18, 0.1)',
            borderWidth: 2,
            tension: 0.1,
            fill: true
          }
        ]
      },
      options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
          title: {
            display: false
          },
          tooltip: {
            mode: 'index',
            intersect: false,
            callbacks: {
              label: function(context) {
                let label = context.dataset.label || '';
                if (label) {
                  label += ': ';
                }
                if (context.parsed.y !== null) {
                  label += '¥' + context.parsed.y.toFixed(4);
                }
                return label;
              }
            }
          }
        },
        scales: {
          x: {
            grid: {
              display: false
            }
          },
          y: {
            beginAtZero: true,
            ticks: {
              callback: function(value) {
                return '¥' + value.toFixed(4);
              }
            }
          }
        }
      }
    });

    // 创建推荐牌阵处理时间图表
    StatisticsState.charts.spreadRecomTimeChart = new Chart(ctxSpreadRecomTime, {
      type: 'line',
      data: {
        labels: [],
        datasets: [
          {
            label: 'AI推荐牌阵处理时间(秒)',
            data: [],
            borderColor: '#8e44ad',
            backgroundColor: 'rgba(142, 68, 173, 0.1)',
            borderWidth: 2,
            tension: 0.1,
            fill: true
          }
        ]
      },
      options: {
        maintainAspectRatio: false,
        plugins: {
          title: {
            display: false
          },
          tooltip: {
            mode: 'index',
            intersect: false,
            callbacks: {
              label: function(context) {
                let label = context.dataset.label || '';
                if (label) {
                  label += ': ';
                }
                if (context.parsed.y !== null) {
                  label += context.parsed.y.toFixed(2) + '秒';
                }
                return label;
              }
            }
          }
        },
        scales: {
          x: {
            grid: {
              display: false
            }
          },
          y: {
            beginAtZero: true,
            ticks: {
              callback: function(value) {
                return value.toFixed(2) + '秒';
              }
            }
          }
        }
      }
    });
    
    StatisticsState.flags.costChartsInitialized = true;
  } catch (error) {
    console.error('初始化成本图表出错:', error);
    showError('初始化成本图表失败: ' + error.message);
  }
}

// 更新成本图表数据
function updateCostCharts() {
  if (!StatisticsState.data.costData || !StatisticsState.flags.costChartsInitialized) {
    console.warn('无法更新成本图表：数据不完整或图表未初始化');
    return;
  }
  
  try {
    const {
      dateRange,
      totalCostData,
      modelInputCostData,
      modelOutputCostData,
      dailyFortuneInputCostData, // 新增：日运输入成本
      dailyFortuneOutputCostData, // 新增：日运输出成本
      // ethicalInputCostData,       // 注释掉伦理检查数据
      // ethicalOutputCostData,      // 注释掉伦理检查数据
      anonymousInputCostData,     // 添加匿名用户输入成本
      anonymousOutputCostData,    // 添加匿名用户输出成本
      anonymousTotalCostData,     // 添加匿名用户总成本
      spreadRecomInputCostData,
      spreadRecomOutputCostData,
      spreadRecomTimeData,
      horoscopeTotalCostData, // 添加星座运势总成本
      ttsCostData,
      summary
    } = StatisticsState.data.costData;
    
    // 检查数据完整性
    if (!dateRange || !dateRange.length) {
      console.warn('日期范围数据不完整');
      return;
    }
    
    // 计算每日模型调用总成本、匿名用户成本和AI推荐牌阵总成本
    const regularModelCostData = {}; // 普通模型（非日运）
    const dailyFortuneCostData = {}; // 日运占卜
    // const ethicalCostData = {};     // 注释掉伦理检查成本
    const anonymousCostData = {};   // 匿名用户成本
    const spreadRecomCostData = {};
    
    dateRange.forEach(date => {
      // 计算普通模型成本（非日运）
      const regularInputCost = dateRange.includes(date) && 
        (summary.totalRegularInputCost !== undefined) ? 
        (modelInputCostData[date] || 0) - (dailyFortuneInputCostData?.[date] || 0) : 
        (modelInputCostData[date] || 0);
      
      const regularOutputCost = dateRange.includes(date) && 
        (summary.totalRegularOutputCost !== undefined) ? 
        (modelOutputCostData[date] || 0) - (dailyFortuneOutputCostData?.[date] || 0) : 
        (modelOutputCostData[date] || 0);
      
      regularModelCostData[date] = regularInputCost + regularOutputCost;
      
      // 计算日运占卜成本
      dailyFortuneCostData[date] = (dailyFortuneInputCostData?.[date] || 0) + (dailyFortuneOutputCostData?.[date] || 0);
      
      // ethicalCostData[date] = (ethicalInputCostData[date] || 0) + (ethicalOutputCostData[date] || 0);  // 注释掉伦理检查成本
      anonymousCostData[date] = anonymousTotalCostData[date] || 0;  // 匿名用户成本
      spreadRecomCostData[date] = (spreadRecomInputCostData[date] || 0) + (spreadRecomOutputCostData[date] || 0);
    });
    
    // 更新每日成本趋势图表
    StatisticsState.charts.costDailyChart.data.labels = dateRange;
    StatisticsState.charts.costDailyChart.data.datasets[0].data = dateRange.map(date => totalCostData[date] || 0);
    StatisticsState.charts.costDailyChart.data.datasets[1].data = dateRange.map(date => regularModelCostData[date] || 0);
    StatisticsState.charts.costDailyChart.data.datasets[2].data = dateRange.map(date => dailyFortuneCostData[date] || 0);
    StatisticsState.charts.costDailyChart.data.datasets[3].data = dateRange.map(date => anonymousCostData[date] || 0); // 匿名用户成本数据
    StatisticsState.charts.costDailyChart.data.datasets[4].data = dateRange.map(date => spreadRecomCostData[date] || 0);
    StatisticsState.charts.costDailyChart.data.datasets[5].data = dateRange.map(date => ttsCostData[date] || 0);
    StatisticsState.charts.costDailyChart.data.datasets[6].data = dateRange.map(date => horoscopeTotalCostData[date] || 0); // 添加星座运势成本数据
    StatisticsState.charts.costDailyChart.update();

    // 更新推荐牌阵处理时间图表
    StatisticsState.charts.spreadRecomTimeChart.data.labels = dateRange;
    StatisticsState.charts.spreadRecomTimeChart.data.datasets[0].data = dateRange.map(date => spreadRecomTimeData[date] || 0);
    StatisticsState.charts.spreadRecomTimeChart.update();
  } catch (error) {
    console.error('更新成本图表出错:', error);
    showError('更新成本图表失败: ' + error.message);
  }
}

// 更新成本统计摘要
function updateCostSummary() {
  if (!StatisticsState.data.costData) return;
  
  try {
    const { summary, dateRange } = StatisticsState.data.costData;
    
    // 更新统计显示
    $('#total-cost').text('¥' + (summary.totalCost || 0).toFixed(4));
    
    // 计算并显示普通模型调用成本（非日运）
    const regularModelCost = (summary.totalRegularInputCost || 0) + (summary.totalRegularOutputCost || 0);
    $('#model-cost').text('¥' + regularModelCost.toFixed(4));
    
    // 计算并显示日运占卜成本
    const dailyFortuneCost = (summary.totalDailyFortuneInputCost || 0) + (summary.totalDailyFortuneOutputCost || 0);
    $('#daily-fortune-cost').text('¥' + dailyFortuneCost.toFixed(4));
    
    // $('#ethical-cost').text('¥' + (summary.totalEthicalCost || 0).toFixed(4));  // 注释掉伦理检查成本
    $('#anonymous-cost').text('¥' + (summary.totalAnonymousCost || 0).toFixed(4)); // 添加匿名用户成本
    $('#spread-recom-cost').text('¥' + (summary.totalSpreadRecomCost || 0).toFixed(4));
    $('#horoscope-cost').text('¥' + (summary.totalHoroscopeCost || 0).toFixed(4)); // 添加星座运势成本
    $('#tts-cost').text('¥' + (summary.totalTtsCost || 0).toFixed(4));
    $('#avg-cost').text('¥' + (summary.avgCost || 0).toFixed(4));
    $('#avg-spread-recom-time').text((summary.avgSpreadRecomTime || 0).toFixed(2) + '秒');
    
    // 更新星座运势相关统计信息
    $('#horoscope-input-tokens').text(summary.totalHoroscopeInputTokens?.toLocaleString() || '0');
    $('#horoscope-output-tokens').text(summary.totalHoroscopeOutputTokens?.toLocaleString() || '0');
    $('#horoscope-count').text(summary.totalHoroscopeCount?.toLocaleString() || '0');
    
    // 根据当前日期范围显示价格
    // 如果日期范围包含6月25日、6月30日、7月10日前后的日期，显示不同价格
    let priceDisplay = '';
    const endDate = dateRange[dateRange.length - 1];
    const startDate = dateRange[0];
    
    if (isAfterJuly10(startDate) && isAfterJuly10(endDate)) {
      // 全部在2025年7月10日之后
      priceDisplay = `输入：¥${PRICES.MODEL_INPUT_JULY10}/千Token<br>输出：¥${PRICES.MODEL_OUTPUT_JULY10}/千Token`;
    } else if (isAfterJune30(startDate) && isAfterJune30(endDate) && !isAfterJuly10(startDate) && !isAfterJuly10(endDate)) {
      // 全部在2025年6月30日之后且7月10日之前
      priceDisplay = `输入：¥${PRICES.MODEL_INPUT_JUNE30}/千Token<br>输出：¥${PRICES.MODEL_OUTPUT_JUNE30}/千Token`;
    } else if (isAfterPriceChange(startDate) && isAfterPriceChange(endDate) && !isAfterJune30(startDate) && !isAfterJune30(endDate)) {
      // 全部在2025年6月25日之后且6月30日之前
      priceDisplay = `输入：¥${PRICES.MODEL_INPUT_NEW}/千Token<br>输出：¥${PRICES.MODEL_OUTPUT_NEW}/千Token`;
    } else if (!isAfterPriceChange(startDate) && !isAfterPriceChange(endDate)) {
      // 全部在2025年6月25日之前
      priceDisplay = `输入：¥${PRICES.MODEL_INPUT}/千Token<br>输出：¥${PRICES.MODEL_OUTPUT}/千Token`;
    } else if (isAfterJuly10(endDate)) {
      // 跨越多个价格变更日期，包括7月10日
      priceDisplay = `6月25日前：<br>输入：¥${PRICES.MODEL_INPUT}/千Token<br>输出：¥${PRICES.MODEL_OUTPUT}/千Token<br>
      6月25日至6月30日前：<br>输入：¥${PRICES.MODEL_INPUT_NEW}/千Token<br>输出：¥${PRICES.MODEL_OUTPUT_NEW}/千Token<br>
      6月30日至7月10日前：<br>输入：¥${PRICES.MODEL_INPUT_JUNE30}/千Token<br>输出：¥${PRICES.MODEL_OUTPUT_JUNE30}/千Token<br>
      7月10日起：<br>输入：¥${PRICES.MODEL_INPUT_JULY10}/千Token<br>输出：¥${PRICES.MODEL_OUTPUT_JULY10}/千Token`;
    } else if (isAfterJune30(endDate)) {
      // 跨越多个价格变更日期，包括6月30日
      priceDisplay = `6月25日前：<br>输入：¥${PRICES.MODEL_INPUT}/千Token<br>输出：¥${PRICES.MODEL_OUTPUT}/千Token<br>
      6月25日至6月30日前：<br>输入：¥${PRICES.MODEL_INPUT_NEW}/千Token<br>输出：¥${PRICES.MODEL_OUTPUT_NEW}/千Token<br>
      6月30日起：<br>输入：¥${PRICES.MODEL_INPUT_JUNE30}/千Token<br>输出：¥${PRICES.MODEL_OUTPUT_JUNE30}/千Token`;
    } else {
      // 跨越6月25日价格变更日期
      priceDisplay = `6月25日前：<br>输入：¥${PRICES.MODEL_INPUT}/千Token<br>输出：¥${PRICES.MODEL_OUTPUT}/千Token<br>
      6月25日起：<br>输入：¥${PRICES.MODEL_INPUT_NEW}/千Token<br>输出：¥${PRICES.MODEL_OUTPUT_NEW}/千Token`;
    }
    
    $('.stat-card.cost-model .stat-price').html(priceDisplay);
    $('.stat-card.cost-daily-fortune .stat-price').html(`输入：¥${PRICES.DAILY_FORTUNE_INPUT}/千Token<br>输出：¥${PRICES.DAILY_FORTUNE_OUTPUT}/千Token`);
    // $('.stat-card.cost-ethical .stat-price').html(`输入：¥${PRICES.ETHICAL_INPUT}/千Token<br>输出：¥${PRICES.ETHICAL_OUTPUT}/千Token`);  // 注释掉伦理检查价格
    $('.stat-card.cost-anonymous .stat-price').html(`输入：¥${PRICES.ANONYMOUS_INPUT}/千Token<br>输出：¥${PRICES.ANONYMOUS_OUTPUT}/千Token`); // 添加匿名用户价格显示
    $('.stat-card.cost-spread-recom .stat-price').html(`输入：¥${PRICES.SPREAD_RECOM_INPUT}/千Token<br>输出：¥${PRICES.SPREAD_RECOM_OUTPUT}/千Token`);
    $('.stat-card.cost-horoscope .stat-price').html(`输入：¥${PRICES.HOROSCOPE_INPUT}/千Token<br>输出：¥${PRICES.HOROSCOPE_OUTPUT}/千Token`); // 添加星座运势价格显示
    $('.stat-card.cost-tts .stat-price').html(`¥${PRICES.TTS}/千字符`);
  } catch (error) {
    console.error('更新成本统计摘要出错:', error);
    showError('更新成本统计摘要失败: ' + error.message);
  }
}

// 获取成本统计数据
function fetchCostStats() {
  const dateRange = $('#daterange').val().split(' - ');
  const startDate = dateRange[0];
  const endDate = dateRange[1];
  
  axios.get('/api/cost-stats', {
    params: {
      startDate,
      endDate
    }
  })
  .then(response => {
    if (response.data.success) {
      StatisticsState.data.costData = response.data.data;
      
      // 初始化成本图表
      initCostCharts();
      
      // 更新成本图表数据
      updateCostCharts();
      
      // 更新成本统计摘要
      updateCostSummary();
    } else {
      showError('获取成本数据失败: ' + response.data.message);
    }
  })
  .catch(error => {
    console.error('获取成本数据失败:', error);
    showError('获取成本数据失败: ' + (error.response?.data?.message || error.message));
  });
}

// 初始化图表标签页切换
$(document).ready(function() {
  // 绑定成本图表切换事件
  $('#cost-stats-container .chart-tab').on('click', function() {
    // 获取要显示的图表类型
    const type = $(this).data('type');
    
    // 更新标签页状态
    $('#cost-stats-container .chart-tab').removeClass('active');
    $(this).addClass('active');
    
    // 隐藏所有图表
    $('#chart-cost-daily, #chart-spread-recom-time').hide();
    
    // 显示选中的图表
    $(`#chart-${type}`).show();

    // 更新图表大小以适应容器
    if (type === 'cost-daily' && StatisticsState.charts.costDailyChart) {
      StatisticsState.charts.costDailyChart.resize();
    } else if (type === 'spread-recom-time' && StatisticsState.charts.spreadRecomTimeChart) {
      StatisticsState.charts.spreadRecomTimeChart.resize();
    }
  });
}); 