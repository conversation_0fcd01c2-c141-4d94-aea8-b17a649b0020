const express = require('express');
const cors = require('cors');
const path = require('path');
const dotenv = require('dotenv');
const { testConnection } = require('./config/db');
const apiRoutes = require('./routes/api');

// 加载环境变量
dotenv.config();

const app = express();
const PORT = process.env.PORT || 3000;

// 中间件
app.use(cors());
app.use(express.json());
app.use(express.urlencoded({ extended: true }));

// 设置视图引擎
app.set('view engine', 'ejs');
app.set('views', path.join(__dirname, '../src/views'));

// 静态文件
app.use(express.static(path.join(__dirname, '../public')));

// API路由
app.use('/api', apiRoutes);

// 页面路由
app.get('/', (req, res) => {
  res.render('index');
});

// 测试数据库连接并启动服务器
const startServer = async () => {
  try {
    await testConnection();
    app.listen(PORT, () => {
      console.log(`服务器已启动，运行在 http://localhost:${PORT}`);
    });
  } catch (error) {
    console.error('服务器启动失败：', error);
    process.exit(1);
  }
};

startServer(); 