const { pool } = require('../config/db');
const moment = require('moment');

// 格式化日期函数
function formatDate(date) {
  return moment(date).format('YYYY-MM-DD');
}

// 获取用户统计数据
async function getUserStats(req, res) {
  try {
    const { startDate, endDate } = req.query;
    const start = startDate || moment().subtract(30, 'days').format('YYYY-MM-DD');
    const end = endDate || moment().format('YYYY-MM-DD');

    // 生成日期范围
    const dateRange = [];
    let current = moment(start);
    const endMoment = moment(end);
    
    while (current.isSameOrBefore(endMoment)) {
      dateRange.push(current.format('YYYY-MM-DD'));
      current.add(1, 'days');
    }

    // 初始化数据结构
    const totalUsersData = {};
    const normalUsersData = {};
    const vipUsersData = {};
    const newUsersData = {};
    const newNormalUsersData = {};
    const newVipUsersData = {};
    const zeroReadingUsersData = {};
    const experienceToRegisterUsersData = {};

    // 获取每天的用户累计数据
    const [dailyTotalStats] = await pool.query(`
      SELECT 
        DATE(created_at) as date,
        COUNT(*) as total_users,
        COUNT(CASE WHEN vip_status = 'none' THEN 1 END) as total_normal_users,
        COUNT(CASE WHEN vip_status != 'none' THEN 1 END) as total_vip_users
      FROM users
      WHERE created_at <= ?
      GROUP BY DATE(created_at)
      ORDER BY date
    `, [end + ' 23:59:59']);

    // 获取每天的新增用户数据
    const [dailyNewStats] = await pool.query(`
      SELECT 
        DATE(created_at) as date,
        COUNT(*) as new_users,
        COUNT(CASE WHEN vip_status = 'none' THEN 1 END) as new_normal_users,
        COUNT(CASE WHEN vip_status != 'none' THEN 1 END) as new_vip_users,
        COUNT(CASE WHEN remaining_reads = 0 THEN 1 END) as zero_remaining_reads_users
      FROM users
      WHERE created_at BETWEEN ? AND ?
      GROUP BY DATE(created_at)
      ORDER BY date
    `, [start + ' 00:00:00', end + ' 23:59:59']);

    // 获取体验后注册用户数据（按日期统计）
    const [experienceToRegisterStats] = await pool.query(`
      SELECT
        DATE(u.created_at) as date,
        COUNT(DISTINCT u.id) as experience_to_register_users
      FROM users u
      INNER JOIN anonymous_divination_records a ON u.ip = a.ip_address
      WHERE u.created_at BETWEEN ? AND ?
        AND a.created_at < u.created_at
        AND u.ip IS NOT NULL
        AND a.ip_address IS NOT NULL
      GROUP BY DATE(u.created_at)
      ORDER BY date
    `, [start + ' 00:00:00', end + ' 23:59:59']);

    // 获取用户国家分布统计
    const [countryStats] = await pool.query(`
      SELECT
        country,
        COUNT(*) as user_count
      FROM users
      WHERE country IS NOT NULL AND country != ''
      GROUP BY country
      ORDER BY user_count DESC
    `);

    // 初始化日期范围内的数据
    dateRange.forEach(date => {
      totalUsersData[date] = 0;
      normalUsersData[date] = 0;
      vipUsersData[date] = 0;
      newUsersData[date] = 0;
      newNormalUsersData[date] = 0;
      newVipUsersData[date] = 0;
      zeroReadingUsersData[date] = 0;
      experienceToRegisterUsersData[date] = 0;
    });

    // 计算每日累计用户数
    let runningTotalUsers = 0;
    let runningNormalUsers = 0;
    let runningVipUsers = 0;

    // 获取开始日期之前的累计用户数
    const [[priorStats]] = await pool.query(`
      SELECT 
        COUNT(*) as total_users,
        COUNT(CASE WHEN vip_status = 'none' THEN 1 END) as total_normal_users,
        COUNT(CASE WHEN vip_status != 'none' THEN 1 END) as total_vip_users
      FROM users
      WHERE created_at < ?
    `, [start + ' 00:00:00']);

    runningTotalUsers = priorStats.total_users || 0;
    runningNormalUsers = priorStats.total_normal_users || 0;
    runningVipUsers = priorStats.total_vip_users || 0;

    // 填充每天的累计用户数
    dailyNewStats.forEach(row => {
      const dateStr = moment(row.date).format('YYYY-MM-DD');
      
      if (dateRange.includes(dateStr)) {
        // 更新累计数
        runningTotalUsers += row.new_users;
        runningNormalUsers += row.new_normal_users;
        runningVipUsers += row.new_vip_users;
        
        // 设置累计值
        totalUsersData[dateStr] = runningTotalUsers;
        normalUsersData[dateStr] = runningNormalUsers;
        vipUsersData[dateStr] = runningVipUsers;
        
        // 设置新增值
        newUsersData[dateStr] = row.new_users;
        newNormalUsersData[dateStr] = row.new_normal_users;
        newVipUsersData[dateStr] = row.new_vip_users;
        zeroReadingUsersData[dateStr] = row.zero_remaining_reads_users || 0;
      }
    });

    // 填充体验后注册用户数据
    experienceToRegisterStats.forEach(row => {
      const dateStr = moment(row.date).format('YYYY-MM-DD');

      if (dateRange.includes(dateStr)) {
        experienceToRegisterUsersData[dateStr] = row.experience_to_register_users || 0;
      }
    });

    // 确保所有日期都有连续的累计值
    let lastTotal = runningTotalUsers;
    let lastNormal = runningNormalUsers;
    let lastVip = runningVipUsers;
    
    dateRange.forEach(date => {
      // 如果当天没有数据，使用上一天的累计值
      if (totalUsersData[date] === 0) {
        totalUsersData[date] = lastTotal;
        normalUsersData[date] = lastNormal;
        vipUsersData[date] = lastVip;
      } else {
        lastTotal = totalUsersData[date];
        lastNormal = normalUsersData[date];
        lastVip = vipUsersData[date];
      }
    });

    res.json({
      success: true,
      data: {
        dateRange,
        totalUsersData,
        normalUsersData,
        vipUsersData,
        newUsersData,
        newNormalUsersData,
        newVipUsersData,
        zeroReadingUsersData,
        experienceToRegisterUsersData,
        countryStats
      }
    });
  } catch (error) {
    console.error('获取用户统计数据失败:', error);
    res.status(500).json({
      success: false,
      message: '获取用户统计数据失败',
      error: error.message
    });
  }
}

module.exports = {
  getUserStats
}; 