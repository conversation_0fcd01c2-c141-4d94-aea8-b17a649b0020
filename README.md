# 页面访问统计分析系统

这是一个基于Node.js和Express的网页应用，用于统计和分析网站页面的访问情况，包括PV（页面浏览量）和UV（独立访客数）数据。

## 功能特点

- 按日期范围查询页面访问数据
- 可视化展示PV和UV趋势
- 支持按页面筛选统计数据
- 实时计算总PV和总UV数据

## 技术栈

- 后端：Node.js, Express
- 数据库：MySQL
- 前端：HTML, CSS, JavaScript, Chart.js
- 工具库：Moment.js, Axios

## 安装与使用

### 前提条件

- Node.js (v14+)
- MySQL数据库

### 安装步骤

1. 克隆仓库
   ```
   git clone [repository-url]
   cd daily_report
   ```

2. 安装依赖
   ```
   npm install
   ```

3. 配置环境变量
   编辑`.env`文件，填入正确的数据库连接信息：
   ```
   DB_HOST=your_aliyun_rds_host.mysql.rds.aliyuncs.com
   DB_USER=your_username
   DB_PASSWORD=your_password
   DB_NAME=tarot
   PORT=3000
   ```

4. 启动应用
   ```
   npm start
   ```
   或开发模式：
   ```
   npm run dev
   ```

5. 访问应用
   打开浏览器访问 http://localhost:3000

## 数据库表结构

应用使用的主要数据表是`page_views`：

```sql
CREATE TABLE `page_views` (
  `id` varchar(36) NOT NULL,
  `user_id` varchar(36) DEFAULT NULL,
  `session_id` varchar(100) NOT NULL,
  `ip_address` varchar(45) NOT NULL,
  `user_agent` text,
  `page_path` varchar(255) NOT NULL,
  `referrer` varchar(255) DEFAULT NULL,
  `timestamp` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_timestamp` (`timestamp`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_session_id` (`session_id`),
  CONSTRAINT `page_views_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
```

## 许可证

ISC 