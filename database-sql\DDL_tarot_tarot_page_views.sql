/******************************************/
/*   DatabaseName = tarot   */
/*   TableName = page_views   */
/******************************************/
CREATE TABLE `page_views` (
  `id` varchar(36) NOT NULL,
  `user_id` varchar(36) DEFAULT NULL,
  `session_id` varchar(100) NOT NULL,
  `ip_address` text NOT NULL,
  `user_agent` text,
  `page_path` text NOT NULL,
  `referrer` text,
  `timestamp` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_timestamp` (`timestamp`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_session_id` (`session_id`),
  CONSTRAINT `page_views_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3
;
