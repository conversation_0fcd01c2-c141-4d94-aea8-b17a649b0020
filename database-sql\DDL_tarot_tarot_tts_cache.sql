/******************************************/
/*   DatabaseName = tarot   */
/*   TableName = tts_cache   */
/******************************************/
CREATE TABLE `tts_cache` (
  `id` varchar(36) COLLATE utf8mb4_unicode_ci NOT NULL,
  `text` text COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '原始文本内容',
  `voice` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '使用的语音',
  `audio_data` mediumblob NOT NULL COMMENT '音频文件二进制数据',
  `created_at` datetime NOT NULL COMMENT '创建时间',
  `expires_at` datetime NOT NULL COMMENT '过期时间',
  `session_id` varchar(36) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '关联的会话ID',
  `user_id` varchar(36) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '关联的用户ID',
  `message_id` varchar(36) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '关联的消息ID',
  `character_count` int unsigned NOT NULL DEFAULT '0' COMMENT '文本字符数量',
  `cost` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '生成费用(元)',
  PRIMARY KEY (`id`),
  KEY `idx_expires_at` (`expires_at`),
  KEY `idx_session_id` (`session_id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_message_id` (`message_id`),
  KEY `idx_character_count` (`character_count`),
  KEY `idx_cost` (`cost`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='TTS语音缓存'
;
