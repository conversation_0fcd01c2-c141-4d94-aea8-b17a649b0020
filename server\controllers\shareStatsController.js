const { pool } = require('../config/db');
const moment = require('moment');

// 获取分享数统计数据
async function getShareStats(req, res) {
  try {
    const { startDate, endDate } = req.query;
    const start = startDate || moment().subtract(30, 'days').format('YYYY-MM-DD');
    const end = endDate || moment().format('YYYY-MM-DD');

    // 生成日期范围
    const dateRange = [];
    const currentDate = moment(start);
    const endMoment = moment(end);
    
    while (currentDate.isSameOrBefore(endMoment)) {
      dateRange.push(currentDate.format('YYYY-MM-DD'));
      currentDate.add(1, 'day');
    }

    // 获取每日分享数趋势数据
    const [dailySharesResult] = await pool.query(`
      SELECT 
        DATE(created_at) as date,
        COUNT(*) as share_count
      FROM share_submissions
      WHERE DATE(created_at) BETWEEN ? AND ?
      GROUP BY DATE(created_at)
      ORDER BY DATE(created_at)
    `, [start, end]);

    // 获取平台分布数据
    const [platformDistributionResult] = await pool.query(`
      SELECT 
        platform,
        COUNT(*) as share_count
      FROM share_submissions
      WHERE DATE(created_at) BETWEEN ? AND ?
      GROUP BY platform
      ORDER BY share_count DESC
    `, [start, end]);

    // 获取状态分布数据
    const [statusDistributionResult] = await pool.query(`
      SELECT 
        status,
        COUNT(*) as share_count
      FROM share_submissions
      WHERE DATE(created_at) BETWEEN ? AND ?
      GROUP BY status
      ORDER BY share_count DESC
    `, [start, end]);

    // 获取每日各状态分享数
    const [dailyStatusResult] = await pool.query(`
      SELECT 
        DATE(created_at) as date,
        status,
        COUNT(*) as share_count
      FROM share_submissions
      WHERE DATE(created_at) BETWEEN ? AND ?
      GROUP BY DATE(created_at), status
      ORDER BY DATE(created_at), status
    `, [start, end]);

    // 获取每日各平台分享数
    const [dailyPlatformResult] = await pool.query(`
      SELECT 
        DATE(created_at) as date,
        platform,
        COUNT(*) as share_count
      FROM share_submissions
      WHERE DATE(created_at) BETWEEN ? AND ?
      GROUP BY DATE(created_at), platform
      ORDER BY DATE(created_at), platform
    `, [start, end]);

    // 获取汇总数据
    const [summaryResult] = await pool.query(`
      SELECT 
        COUNT(*) as total_shares,
        SUM(CASE WHEN status = 'pending' THEN 1 ELSE 0 END) as pending_shares,
        SUM(CASE WHEN status = 'approved' THEN 1 ELSE 0 END) as approved_shares,
        SUM(CASE WHEN status = 'rejected' THEN 1 ELSE 0 END) as rejected_shares,
        SUM(CASE WHEN reward_granted = 1 THEN 1 ELSE 0 END) as rewarded_shares
      FROM share_submissions
      WHERE DATE(created_at) BETWEEN ? AND ?
    `, [start, end]);

    // 获取可用平台列表
    const [platformsResult] = await pool.query(`
      SELECT DISTINCT platform
      FROM share_submissions
      WHERE DATE(created_at) BETWEEN ? AND ?
      ORDER BY platform
    `, [start, end]);

    // 初始化数据结构
    const dailySharesData = {};
    const dailyStatusData = {
      pending: {},
      approved: {},
      rejected: {}
    };
    const dailyPlatformData = {};

    // 初始化所有日期的数据为0
    dateRange.forEach(date => {
      dailySharesData[date] = 0;
      dailyStatusData.pending[date] = 0;
      dailyStatusData.approved[date] = 0;
      dailyStatusData.rejected[date] = 0;
    });

    // 填充每日分享数数据
    dailySharesResult.forEach(row => {
      const dateStr = moment(row.date).format('YYYY-MM-DD');
      if (dateRange.includes(dateStr)) {
        dailySharesData[dateStr] = row.share_count;
      }
    });

    // 填充每日状态数据
    dailyStatusResult.forEach(row => {
      const dateStr = moment(row.date).format('YYYY-MM-DD');
      if (dateRange.includes(dateStr) && dailyStatusData[row.status]) {
        dailyStatusData[row.status][dateStr] = row.share_count;
      }
    });

    // 初始化平台数据
    const platforms = platformsResult.map(row => row.platform);
    platforms.forEach(platform => {
      dailyPlatformData[platform] = {};
      dateRange.forEach(date => {
        dailyPlatformData[platform][date] = 0;
      });
    });

    // 填充每日平台数据
    dailyPlatformResult.forEach(row => {
      const dateStr = moment(row.date).format('YYYY-MM-DD');
      if (dateRange.includes(dateStr) && dailyPlatformData[row.platform]) {
        dailyPlatformData[row.platform][dateStr] = row.share_count;
      }
    });

    const summary = summaryResult[0] || {
      total_shares: 0,
      pending_shares: 0,
      approved_shares: 0,
      rejected_shares: 0,
      rewarded_shares: 0
    };

    res.json({
      success: true,
      data: {
        dateRange,
        dailySharesData,
        dailyStatusData,
        dailyPlatformData,
        platformDistribution: platformDistributionResult,
        statusDistribution: statusDistributionResult,
        platforms,
        summary
      }
    });
  } catch (error) {
    console.log('获取分享统计数据失败:', error);
    res.status(500).json({
      success: false,
      message: '获取分享统计数据失败',
      error: error.message
    });
  }
}

module.exports = {
  getShareStats
};
