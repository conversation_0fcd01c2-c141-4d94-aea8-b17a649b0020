const { pool } = require('../config/db');
const moment = require('moment');

// 格式化日期函数
function formatDate(date) {
  return moment(date).format('YYYY-MM-DD');
}

// 检查是否是安全检测会话
function isSecuritySession(ethical_status) {
  return ethical_status === 'ethical_intervention' || ethical_status === 'ethical_intervention_follow';
}

// 检查是否是潜在安全检测会话
function isPotentialSecuritySession(ethical_status) {
  return ethical_status === 'potential_ethical_issue' || ethical_status === 'potential_ethical_issue_follow';
}

// 检查是否是占卜会话
function isDivinationSession(spread_id) {
  return spread_id === 'yes-no' || spread_id === 'yes-no-single-card';
}

// 获取sessions统计数据
async function getSessionStats(req, res) {
  try {
    const { startDate, endDate } = req.query;
    const start = startDate || moment().subtract(30, 'days').format('YYYY-MM-DD');
    const end = endDate || moment().format('YYYY-MM-DD');

    // 生成日期范围
    const dateRange = [];
    let current = moment(start);
    const endMoment = moment(end);
    
    while (current.isSameOrBefore(endMoment)) {
      dateRange.push(current.format('YYYY-MM-DD'));
      current.add(1, 'days');
    }

    // 初始化数据结构
    const totalSessionsData = {};
    const newSessionsData = {};
    const pendingSessionsData = {};
    const completedSessionsData = {};
    const singleNewlineData = {}; // 单行换行符会话数据
    const doubleNewlineData = {}; // 双行换行符会话数据
    const paragraphDetectionSuccessData = {}; // 分段检测成功会话数据
    const paragraphDetectionFailData = {}; // 分段检测失败会话数据
    const dailyFortuneSessionsData = {}; // 日运会话数据
    const divinationSessionsData = {}; // 占卜会话数据
    const anonymousSessionsData = {}; // 匿名会话数据

    // 获取每天的新增sessions数据（登录用户）
    const [dailyNewStats] = await pool.query(`
      SELECT
        DATE(timestamp) as date,
        COUNT(*) as new_sessions,
        COUNT(CASE WHEN status = 'pending' THEN 1 END) as pending_sessions,
        COUNT(CASE WHEN status != 'pending' THEN 1 END) as completed_sessions,
        COUNT(CASE WHEN newline_type = 'single' THEN 1 END) as single_newline,
        COUNT(CASE WHEN newline_type = 'double' THEN 1 END) as double_newline,
        COUNT(CASE WHEN paragraph_detection_status = 1 THEN 1 END) as paragraph_success,
        COUNT(CASE WHEN paragraph_detection_status = 0 THEN 1 END) as paragraph_fail,
        COUNT(CASE WHEN spread_id = 'daily-fortune' THEN 1 END) as daily_fortune_sessions,
        COUNT(CASE WHEN spread_id IN ('yes-no', 'yes-no-single-card') THEN 1 END) as divination_sessions
      FROM sessions
      WHERE timestamp BETWEEN ? AND ?
      GROUP BY DATE(timestamp)
      ORDER BY date
    `, [start + ' 00:00:00', end + ' 23:59:59']);

    // 获取每天的匿名会话数据（按session_id去重）
    const [dailyAnonymousStats] = await pool.query(`
      SELECT
        DATE(created_at) as date,
        COUNT(DISTINCT session_id) as anonymous_sessions
      FROM anonymous_divination_records
      WHERE created_at BETWEEN ? AND ?
      GROUP BY DATE(created_at)
      ORDER BY date
    `, [start + ' 00:00:00', end + ' 23:59:59']);

    // 获取开始日期之前的累计sessions数（包含匿名会话）
    const [[priorStats]] = await pool.query(`
      SELECT
        (SELECT COUNT(*) FROM sessions WHERE timestamp < ?) +
        (SELECT COUNT(DISTINCT session_id) FROM anonymous_divination_records WHERE created_at < ?) as total_sessions
    `, [start + ' 00:00:00', start + ' 00:00:00']);

    // 获取开始日期之前的累计匿名会话数
    const [[priorAnonymousStats]] = await pool.query(`
      SELECT COUNT(DISTINCT session_id) as total_anonymous_sessions
      FROM anonymous_divination_records
      WHERE created_at < ?
    `, [start + ' 00:00:00']);

    let runningTotalSessions = priorStats.total_sessions || 0;
    let runningTotalAnonymousSessions = priorAnonymousStats.total_anonymous_sessions || 0;

    // 初始化日期范围内的数据
    dateRange.forEach(date => {
      totalSessionsData[date] = runningTotalSessions;
      newSessionsData[date] = 0;
      pendingSessionsData[date] = 0;
      completedSessionsData[date] = 0;
      singleNewlineData[date] = 0;
      doubleNewlineData[date] = 0;
      paragraphDetectionSuccessData[date] = 0;
      paragraphDetectionFailData[date] = 0;
      dailyFortuneSessionsData[date] = 0;
      divinationSessionsData[date] = 0;
      anonymousSessionsData[date] = 0;
    });

    // 填充每天的新增sessions数据（登录用户）
    dailyNewStats.forEach(row => {
      const dateStr = moment(row.date).format('YYYY-MM-DD');

      if (dateRange.includes(dateStr)) {
        // 设置新增值（仅登录用户）
        newSessionsData[dateStr] = row.new_sessions;
        pendingSessionsData[dateStr] = row.pending_sessions;
        completedSessionsData[dateStr] = row.completed_sessions;
        singleNewlineData[dateStr] = row.single_newline;
        doubleNewlineData[dateStr] = row.double_newline;
        paragraphDetectionSuccessData[dateStr] = row.paragraph_success;
        paragraphDetectionFailData[dateStr] = row.paragraph_fail;
        dailyFortuneSessionsData[dateStr] = row.daily_fortune_sessions;
        divinationSessionsData[dateStr] = row.divination_sessions;
      }
    });

    // 填充每天的匿名会话数据
    dailyAnonymousStats.forEach(row => {
      const dateStr = moment(row.date).format('YYYY-MM-DD');

      if (dateRange.includes(dateStr)) {
        anonymousSessionsData[dateStr] = row.anonymous_sessions;
      }
    });

    // 计算累计总会话数（包含匿名会话）和更新新增会话数（包含匿名会话）
    dateRange.forEach(date => {
      const dailyLoginSessions = newSessionsData[date] || 0;
      const dailyAnonymousSessions = anonymousSessionsData[date] || 0;

      // 更新累计总数
      runningTotalSessions += dailyLoginSessions;
      runningTotalAnonymousSessions += dailyAnonymousSessions;
      totalSessionsData[date] = runningTotalSessions + runningTotalAnonymousSessions;

      // 更新新增会话数（包含匿名会话）
      newSessionsData[date] = dailyLoginSessions + dailyAnonymousSessions;
    });

    // 确保所有日期都有连续的累计值
    let lastTotal = totalSessionsData[dateRange[0]] || (runningTotalSessions + runningTotalAnonymousSessions);

    dateRange.forEach((date, index) => {
      // 如果当天没有数据，使用上一天的累计值
      if (index > 0 && totalSessionsData[date] === lastTotal) {
        totalSessionsData[date] = lastTotal;
      } else {
        lastTotal = totalSessionsData[date];
      }
    });

    res.json({
      success: true,
      data: {
        dateRange,
        totalSessionsData,
        newSessionsData,
        pendingSessionsData,
        completedSessionsData,
        singleNewlineData,
        doubleNewlineData,
        paragraphDetectionSuccessData,
        paragraphDetectionFailData,
        dailyFortuneSessionsData,
        divinationSessionsData,
        anonymousSessionsData
      }
    });
  } catch (error) {
    console.error('获取sessions统计数据失败:', error);
    res.status(500).json({
      success: false,
      message: '获取sessions统计数据失败',
      error: error.message
    });
  }
}

// 获取sessions明细数据
async function getSessionDetails(req, res) {
  try {
    const { startDate, endDate, limit = 100, offset = 0, query = '', status = '', sessionId = '' } = req.query;
    const start = startDate || moment().subtract(30, 'days').format('YYYY-MM-DD');
    const end = endDate || moment().format('YYYY-MM-DD');
    
    // 确保limit和offset是整数，并防止负值
    const limitInt = Math.max(1, parseInt(limit, 10) || 20);
    const offsetInt = Math.max(0, parseInt(offset, 10) || 0);
    
    // 计算当前页码
    const currentPage = Math.floor(offsetInt / limitInt) + 1;
    

    // 构建查询条件
    let whereClause = 'timestamp BETWEEN ? AND ?';
    let queryParams = [start + ' 00:00:00', end + ' 23:59:59'];
    
    // 如果有会话ID查询，添加到查询条件
    if (sessionId) {
      whereClause += ' AND id = ?';
      queryParams.push(sessionId);
    }
    
    // 如果有搜索关键词，添加到查询条件
    if (query) {
      whereClause += ' AND question LIKE ?';
      queryParams.push(`%${query}%`);
    }
    
    // 如果指定了状态筛选，添加到查询条件
    if (status) {
      if (status === 'ethical_intervention') {
        whereClause += ' AND (ethical_status = ? OR ethical_status = ?)';
        queryParams.push('ethical_intervention', 'ethical_intervention_follow');
      } else if (status === 'potential_ethical_issue') {
        whereClause += ' AND (ethical_status = ? OR ethical_status = ?)';
        queryParams.push('potential_ethical_issue', 'potential_ethical_issue_follow');
      } else {
        whereClause += ' AND status = ?';
        queryParams.push(status);
      }
    }

    // 查询sessions明细
    const [sessionsData] = await pool.query(`
      SELECT 
        id,
        user_id,
        question,
        status,
        ethical_status,
        timestamp,
        newline_type,
        paragraph_detection_status,
        spread_id
      FROM sessions
      WHERE ${whereClause}
      ORDER BY timestamp DESC
    `, queryParams);
    
    // 处理安全检测标记和占卜会话标记
    const sessions = sessionsData.map(session => {
      return {
        ...session,
        is_security: isSecuritySession(session.ethical_status) ? 1 : 0,
        is_potential_security: isPotentialSecuritySession(session.ethical_status) ? 1 : 0,
        is_divination: isDivinationSession(session.spread_id) ? 1 : 0
      };
    });
    
    // 计算总记录数
    const total = sessions.length;
    const totalPages = Math.ceil(total / limitInt);
    
    // 手动分页
    const paginatedSessions = sessions.slice(offsetInt, offsetInt + limitInt);

    res.json({
      success: true,
      data: {
        sessions: paginatedSessions,
        total: total,
        limit: limitInt,
        offset: offsetInt,
        currentPage: currentPage,
        totalPages: totalPages
      }
    });
  } catch (error) {
    console.error('获取sessions明细数据失败:', error);
    res.status(500).json({
      success: false,
      message: '获取sessions明细数据失败',
      error: error.message
    });
  }
}

// 获取单个会话详情
async function getSessionDetail(req, res) {
  try {
    const { id } = req.params;
    
    if (!id) {
      return res.status(400).json({
        success: false,
        message: '会话ID不能为空'
      });
    }
    
    // 查询会话详情
    const [[sessionData]] = await pool.query(`
      SELECT 
        id,
        user_id,
        question,
        status,
        ethical_status,
        timestamp,
        reading_result,
        deep_analysis,
        dialog_history,
        Ethical_category,
        Ethical_reason,
        Ethical_confidence,
        followup_ethical_categories,
        followup_ethical_reasons,
        newline_type,
        paragraph_detection_status,
        spread_id
      FROM sessions
      WHERE id = ?
    `, [id]);
    
    if (!sessionData) {
      console.log(`未找到ID为 ${id} 的会话`);
      return res.status(404).json({
        success: false,
        message: '未找到指定的会话'
      });
    }
    
    // 添加安全检测标记和占卜会话标记
    const session = {
      ...sessionData,
      is_security: isSecuritySession(sessionData.ethical_status) ? 1 : 0,
      is_potential_security: isPotentialSecuritySession(sessionData.ethical_status) ? 1 : 0,
      is_divination: isDivinationSession(sessionData.spread_id) ? 1 : 0
    };
    
    // 确保dialog_history是JSON字符串
    if (session.dialog_history && typeof session.dialog_history === 'object') {
      session.dialog_history = JSON.stringify(session.dialog_history);
    }
    
    res.json({
      success: true,
      data: session
    });
  } catch (error) {
    console.error('获取会话详情失败:', error);
    res.status(500).json({
      success: false,
      message: '获取会话详情失败',
      error: error.message
    });
  }
}

module.exports = {
  getSessionStats,
  getSessionDetails,
  getSessionDetail
}; 