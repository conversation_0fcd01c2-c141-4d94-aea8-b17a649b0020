// 统计模块的状态管理
const StatisticsState = {
  // 图表实例
  charts: {
    shareTrendChart: null,
    sharePlatformChart: null,
    shareStatusChart: null,
    userTotalChart: null,
    userNewChart: null,
    userCountryChart: null,
    sessionTotalChart: null,
    sessionNewChart: null,
    sessionFormatChart: null,
    costDailyChart: null,
    spreadRecomTimeChart: null,
    incomeDailyChart: null,
    incomeProductChart: null
  },
  // 数据存储
  data: {
    shareData: null,
    userData: null,
    sessionData: null,
    costData: null,
    incomeData: null
  },
  // 状态标志
  flags: {
    shareChartsInitialized: false,
    userChartsInitialized: false,
    sessionChartsInitialized: false,
    costChartsInitialized: false,
    incomeChartsInitialized: false
  }
};

// 错误处理函数
function showError(message) {
  const errorContainer = document.getElementById('error-container');
  errorContainer.style.display = 'block';
  errorContainer.textContent = message;
}

function hideError() {
  const errorContainer = document.getElementById('error-container');
  errorContainer.style.display = 'none';
}

// 重新创建canvas元素
function recreateCanvasElements() {
  const canvasConfigs = [
    { id: 'shareTrendChart', wrapper: 'chart-share-trend' },
    { id: 'sharePlatformChart', wrapper: 'chart-share-platform' },
    { id: 'shareStatusChart', wrapper: 'chart-share-status' },
    { id: 'userTotalChart', wrapper: 'chart-user-total' },
    { id: 'userNewChart', wrapper: 'chart-user-new' },
    { id: 'userCountryChart', wrapper: 'chart-user-country' },
    { id: 'sessionTotalChart', wrapper: 'chart-session-total' },
    { id: 'sessionNewChart', wrapper: 'chart-session-new' },
    { id: 'sessionFormatChart', wrapper: 'chart-session-format' },
    { id: 'costDailyChart', wrapper: 'chart-cost-daily' },
    { id: 'spreadRecomTimeChart', wrapper: 'chart-spread-recom-time' },
    { id: 'incomeDailyChart', wrapper: 'chart-income-daily' },
    { id: 'incomeProductChart', wrapper: 'chart-income-product' }
  ];
  
  canvasConfigs.forEach(config => {
    const { id, wrapper } = config;
    const wrapperElement = document.getElementById(wrapper);
    if (!wrapperElement) {
      console.error(`找不到图表容器: ${wrapper}`);
      return;
    }
    
    // 清空容器
    wrapperElement.innerHTML = '';
    
    // 创建新的canvas元素
    const canvas = document.createElement('canvas');
    canvas.id = id; // 使用配置中的ID
    canvas.style.width = '100%';
    canvas.style.height = '100%';
    wrapperElement.appendChild(canvas);
  });
}

// 获取数据
function fetchData() {
  hideError(); // 隐藏之前的错误信息
  
  const dateRange = $('#daterange').val().split(' - ');
  const startDate = dateRange[0];
  const endDate = dateRange[1];
  
  // 显示加载状态
  $('.chart-wrapper').each(function() {
    $(this).html('<div class="loading">数据加载中...</div>');
  });
  
  // 重置图表状态
  StatisticsState.flags.shareChartsInitialized = false;
  StatisticsState.flags.userChartsInitialized = false;
  StatisticsState.flags.sessionChartsInitialized = false;
  StatisticsState.flags.costChartsInitialized = false;
  StatisticsState.flags.incomeChartsInitialized = false;
  
  // 重新创建Canvas元素
  recreateCanvasElements();

  // 获取分享数统计数据
  fetchShareStats();

  // 获取用户统计数据
  axios.get('/api/user-stats', {
    params: {
      startDate,
      endDate
    }
  })
  .then(response => {
    if (response.data.success) {
      StatisticsState.data.userData = response.data.data;
      
      // 初始化用户图表
      initUserCharts();
      
      // 更新用户图表数据
      updateUserCharts();
      
      // 更新用户统计摘要
      updateUserSummary();
    } else {
      showError('获取用户数据失败: ' + response.data.message);
    }
  })
  .catch(error => {
    console.error('获取用户数据失败:', error);
    showError('获取用户数据失败: ' + (error.response?.data?.message || error.message));
  });

  // 获取sessions统计数据
  axios.get('/api/session-stats', {
    params: {
      startDate,
      endDate
    }
  })
  .then(response => {
    if (response.data.success) {
      StatisticsState.data.sessionData = response.data.data;
      
      // 初始化sessions图表
      initSessionCharts();
      
      // 更新sessions图表数据
      updateSessionCharts();
      
      // 更新sessions统计摘要
      updateSessionSummary();
      
      // 初始化会话明细功能
      initSessionDetails();
    } else {
      showError('获取会话数据失败: ' + response.data.message);
    }
  })
  .catch(error => {
    console.error('获取会话数据失败:', error);
    showError('获取会话数据失败: ' + (error.response?.data?.message || error.message));
  });

  // 获取成本统计数据
  fetchCostStats();
  
  // 获取收入统计数据
  fetchIncomeStats();
}

// 初始化日期选择器和页面筛选功能
$(function() {
  const start = moment().subtract(29, 'days');
  const end = moment();

  $('#daterange').daterangepicker({
    startDate: start,
    endDate: end,
    ranges: {
      '今天': [moment(), moment()],
      '昨天': [moment().subtract(1, 'days'), moment().subtract(1, 'days')],
      '最近7天': [moment().subtract(6, 'days'), moment()],
      '最近30天': [moment().subtract(29, 'days'), moment()],
      '本月': [moment().startOf('month'), moment().endOf('month')],
      '上月': [moment().subtract(1, 'month').startOf('month'), moment().subtract(1, 'month').endOf('month')]
    },
    locale: {
      format: 'YYYY-MM-DD',
      applyLabel: '确定',
      cancelLabel: '取消',
      customRangeLabel: '自定义范围'
    }
  });

  // 预先创建Canvas元素
  recreateCanvasElements();

  // 加载初始数据
  fetchData();

  // 绑定事件
  $('#daterange').on('apply.daterangepicker', function(ev, picker) {
    // 重置会话明细分页状态
    if (window.SessionDetails) {
      console.log('日期范围改变，重置分页状态');
      window.SessionDetails.currentPage = 1;
      window.SessionDetails.totalPages = 1;
    }
    fetchData();
  });

  $('#refresh').on('click', function() {
    fetchData();
  });

  $('#page-select').on('change', function() {
    StatisticsState.data.currentPage = $(this).val();
    updateCharts();
  });
  
  // 绑定图表切换事件
  $('.chart-tab').on('click', function() {
    const type = $(this).data('type');
    $(this).parent().find('.chart-tab').removeClass('active');
    $(this).addClass('active');
    
    $(this).closest('.chart-container').find('.chart-wrapper').hide();
    $(`#chart-${type}`).show();
  });
  
  // 当点击弹窗外部区域时关闭弹窗
  $(window).on('click', function(event) {
    if ($(event.target).is('#main-pages-modal')) {
      $('#main-pages-modal').hide();
    }
  });

  // 绑定数据类型切换事件
  $('.data-type-tab').on('click', function() {
    // 获取要显示的数据类型
    const type = $(this).data('type');
    
    // 更新标签页状态
    $('.data-type-tab').removeClass('active');
    $(this).addClass('active');
    
    // 隐藏所有数据容器
    $('#share-stats-container, #user-stats-container, #session-stats-container, #cost-stats-container, #income-stats-container, #diary-container').hide();

    // 显示选中的数据容器
    $(`#${type}-stats-container, #${type}-container`).show();

    // 根据选中的数据类型加载相应的数据
    if (type === 'share') {
      fetchShareStats();
    } else if (type === 'user') {
      fetchUserStats();
    } else if (type === 'session') {
      fetchSessionStats();
    } else if (type === 'cost') {
      fetchCostStats();
    } else if (type === 'income') {
      fetchIncomeStats();
    } else if (type === 'diary') {
      loadDiaries();
    }
  });
});

// 导出状态和函数供其他模块使用
window.StatisticsState = StatisticsState;
window.showError = showError;
window.hideError = hideError; 