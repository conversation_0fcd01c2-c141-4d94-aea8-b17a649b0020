// 日记功能相关代码

// 日记数据存储在本地存储中
let diaries = JSON.parse(localStorage.getItem('diaries')) || [];

// 初始化日记功能
function initDiary() {
  // 设置今天的日期为默认值
  $('#diary-date').val(new Date().toISOString().split('T')[0]);
  
  // 绑定添加日记按钮事件
  $('#add-diary').on('click', function() {
    $('#diary-modal-title').text('添加新日记');
    $('#diary-date').val(new Date().toISOString().split('T')[0]);
    $('#diary-title').val('');
    $('#diary-content').val('');
    $('#diary-modal').show();
  });
  
  // 绑定保存日记按钮事件
  $('#save-diary').on('click', function() {
    const date = $('#diary-date').val();
    const title = $('#diary-title').val().trim();
    const content = $('#diary-content').val().trim();
    
    if (!date || !title || !content) {
      alert('请填写所有必填字段');
      return;
    }
    
    // 添加新日记
    const newDiary = {
      id: Date.now().toString(),
      date,
      title,
      content,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };
    diaries.unshift(newDiary);
    
    // 保存到本地存储
    localStorage.setItem('diaries', JSON.stringify(diaries));
    
    // 关闭模态框
    $('#diary-modal').hide();
    
    // 重新加载日记列表
    loadDiaries();
  });
  
  // 绑定取消按钮事件
  $('#cancel-diary, .close-modal').on('click', function() {
    $('#diary-modal').hide();
  });
  
  // 点击模态框外部关闭
  $(window).on('click', function(event) {
    if ($(event.target).hasClass('modal')) {
      $(event.target).hide();
    }
  });
}

// 加载日记列表
function loadDiaries() {
  const diaryList = $('.diary-list');
  diaryList.empty();
  
  // 按日期降序排序
  diaries.sort((a, b) => new Date(b.date) - new Date(a.date));
  
  if (diaries.length === 0) {
    diaryList.html('<div class="no-data">暂无日记记录，点击"添加新日记"开始记录</div>');
    return;
  }
  
  // 遍历日记并创建卡片
  diaries.forEach(diary => {
    const diaryCard = $('<div>').addClass('diary-card');
    
    const diaryHeader = $('<div>').addClass('diary-header');
    const diaryTitle = $('<h3>').addClass('diary-title').text(diary.title);
    const diaryDate = $('<div>').addClass('diary-date').text(formatDate(diary.date));
    
    // 将标题和日期分开放置，不再使用flex布局
    diaryHeader.append(diaryTitle);
    diaryHeader.append(diaryDate);
    
    const diaryContent = $('<div>').addClass('diary-content').text(diary.content);
    
    diaryCard.append(diaryHeader, diaryContent);
    diaryList.append(diaryCard);
  });
}

// 格式化日期
function formatDate(dateString) {
  const options = { year: 'numeric', month: 'long', day: 'numeric', weekday: 'long' };
  return new Date(dateString).toLocaleDateString('zh-CN', options);
}

// 页面加载完成后初始化
$(document).ready(function() {
  initDiary();
}); 