// 初始化分享数统计图表
function initShareCharts() {
  try {
    // 确保Canvas元素存在并可用
    function getCanvasContext(id) {
      const canvas = document.getElementById(id);
      if (!canvas) {
        throw new Error(`找不到Canvas元素: ${id}`);
      }
      return canvas.getContext('2d');
    }
    
    const ctxTrend = getCanvasContext('shareTrendChart');
    const ctxPlatform = getCanvasContext('sharePlatformChart');
    const ctxStatus = getCanvasContext('shareStatusChart');
    
    // 销毁之前的图表实例
    if (StatisticsState.charts.shareTrendChart) StatisticsState.charts.shareTrendChart.destroy();
    if (StatisticsState.charts.sharePlatformChart) StatisticsState.charts.sharePlatformChart.destroy();
    if (StatisticsState.charts.shareStatusChart) StatisticsState.charts.shareStatusChart.destroy();
    
    // 创建分享趋势图表
    StatisticsState.charts.shareTrendChart = new Chart(ctxTrend, {
      type: 'line',
      data: {
        labels: [],
        datasets: [{
          label: '每日分享数',
          data: [],
          borderColor: '#3498db',
          backgroundColor: 'rgba(52, 152, 219, 0.1)',
          borderWidth: 2,
          tension: 0.1,
          fill: true
        }]
      },
      options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
          title: {
            display: false
          },
          legend: {
            display: false
          },
          tooltip: {
            mode: 'index',
            intersect: false
          }
        },
        scales: {
          x: {
            grid: {
              display: false
            }
          },
          y: {
            beginAtZero: true,
            ticks: {
              precision: 0
            }
          }
        }
      }
    });

    // 创建平台分布图表
    StatisticsState.charts.sharePlatformChart = new Chart(ctxPlatform, {
      type: 'doughnut',
      data: {
        labels: [],
        datasets: [{
          data: [],
          backgroundColor: [
            '#3498db',
            '#2ecc71',
            '#f39c12',
            '#e74c3c',
            '#9b59b6',
            '#1abc9c',
            '#34495e',
            '#f1c40f'
          ],
          borderWidth: 2,
          borderColor: '#fff'
        }]
      },
      options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
          legend: {
            position: 'bottom'
          },
          tooltip: {
            callbacks: {
              label: function(context) {
                const label = context.label || '';
                const value = context.raw || 0;
                const total = context.dataset.data.reduce((a, b) => a + b, 0);
                const percentage = total > 0 ? Math.round((value / total) * 100) : 0;
                return `${label}: ${value} (${percentage}%)`;
              }
            }
          }
        }
      }
    });

    // 创建状态分布图表
    StatisticsState.charts.shareStatusChart = new Chart(ctxStatus, {
      type: 'bar',
      data: {
        labels: [],
        datasets: [{
          label: '分享数量',
          data: [],
          backgroundColor: [
            '#f39c12', // pending - 橙色
            '#2ecc71', // approved - 绿色
            '#e74c3c'  // rejected - 红色
          ],
          borderWidth: 1,
          borderColor: '#fff'
        }]
      },
      options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
          legend: {
            display: false
          },
          tooltip: {
            callbacks: {
              label: function(context) {
                const label = context.label || '';
                const value = context.raw || 0;
                return `${label}: ${value}`;
              }
            }
          }
        },
        scales: {
          x: {
            grid: {
              display: false
            }
          },
          y: {
            beginAtZero: true,
            ticks: {
              precision: 0
            }
          }
        }
      }
    });
    
    StatisticsState.flags.shareChartsInitialized = true;
  } catch (error) {
    console.error('初始化分享数图表出错:', error);
    showError('初始化分享数图表失败: ' + error.message);
  }
}

// 更新分享数图表数据
function updateShareCharts() {
  if (!StatisticsState.data.shareData || !StatisticsState.flags.shareChartsInitialized) {
    console.warn('无法更新分享数图表：数据不完整或图表未初始化');
    return;
  }

  try {
    const { dateRange, dailySharesData, platformDistribution, statusDistribution } = StatisticsState.data.shareData;

    // 更新分享趋势图表
    const trendData = dateRange.map(date => dailySharesData[date] || 0);
    StatisticsState.charts.shareTrendChart.data.labels = dateRange;
    StatisticsState.charts.shareTrendChart.data.datasets[0].data = trendData;
    StatisticsState.charts.shareTrendChart.update();

    // 更新平台分布图表
    const platformLabels = platformDistribution.map(item => item.platform);
    const platformData = platformDistribution.map(item => item.share_count);
    StatisticsState.charts.sharePlatformChart.data.labels = platformLabels;
    StatisticsState.charts.sharePlatformChart.data.datasets[0].data = platformData;
    StatisticsState.charts.sharePlatformChart.update();

    // 更新状态分布图表
    const statusLabels = statusDistribution.map(item => {
      const statusMap = {
        'pending': '待审核',
        'approved': '已通过',
        'rejected': '已拒绝'
      };
      return statusMap[item.status] || item.status;
    });
    const statusData = statusDistribution.map(item => item.share_count);
    StatisticsState.charts.shareStatusChart.data.labels = statusLabels;
    StatisticsState.charts.shareStatusChart.data.datasets[0].data = statusData;
    StatisticsState.charts.shareStatusChart.update();

  } catch (error) {
    console.error('更新分享数图表出错:', error);
    showError('更新分享数图表失败: ' + error.message);
  }
}

// 更新分享数统计摘要
function updateShareSummary() {
  if (!StatisticsState.data.shareData) {
    console.warn('无法更新分享数统计摘要：数据不完整');
    return;
  }

  try {
    const { summary } = StatisticsState.data.shareData;
    
    // 更新统计显示
    $('#total-shares').text((summary.total_shares || 0).toLocaleString());
    $('#pending-shares').text((summary.pending_shares || 0).toLocaleString());
    $('#approved-shares').text((summary.approved_shares || 0).toLocaleString());
    $('#rewarded-shares').text((summary.rewarded_shares || 0).toLocaleString());
    
  } catch (error) {
    console.error('更新分享数统计摘要出错:', error);
    showError('更新分享数统计摘要失败: ' + error.message);
  }
}

// 更新平台选择器
function updatePlatformSelector() {
  if (!StatisticsState.data.shareData) {
    return;
  }

  const platformSelect = $('#platform-select');
  platformSelect.empty();
  
  // 添加"全部平台"选项
  platformSelect.append($('<option>', {
    value: 'all',
    text: '全部平台'
  }));
  
  // 添加各个平台选项
  const { platforms } = StatisticsState.data.shareData;
  platforms.forEach(platform => {
    platformSelect.append($('<option>', {
      value: platform,
      text: platform
    }));
  });
}

// 获取分享数统计数据
function fetchShareStats() {
  const dateRange = $('#daterange').val().split(' - ');
  const startDate = dateRange[0];
  const endDate = dateRange[1];
  
  axios.get('/api/share-stats', {
    params: {
      startDate,
      endDate
    }
  })
  .then(response => {
    if (response.data.success) {
      StatisticsState.data.shareData = response.data.data;
      
      // 初始化分享数图表
      initShareCharts();
      
      // 更新分享数图表数据
      updateShareCharts();
      
      // 更新分享数统计摘要
      updateShareSummary();
      
      // 更新平台选择器
      updatePlatformSelector();
    } else {
      showError('获取分享数据失败: ' + response.data.message);
    }
  })
  .catch(error => {
    console.error('获取分享数据失败:', error);
    showError('获取分享数据失败: ' + (error.response?.data?.message || error.message));
  });
}

// 初始化图表标签页切换
$(document).ready(function() {
  // 绑定分享数图表切换事件
  $('#share-stats-container .chart-tab').on('click', function() {
    // 获取要显示的图表类型
    const type = $(this).data('type');
    
    // 更新标签页状态
    $('#share-stats-container .chart-tab').removeClass('active');
    $(this).addClass('active');
    
    // 隐藏所有图表
    $('#chart-share-trend, #chart-share-platform, #chart-share-status').hide();
    
    // 显示选中的图表
    $(`#chart-${type}`).show();

    // 更新图表大小以适应容器
    if (type === 'share-trend' && StatisticsState.charts.shareTrendChart) {
      StatisticsState.charts.shareTrendChart.resize();
    } else if (type === 'share-platform' && StatisticsState.charts.sharePlatformChart) {
      StatisticsState.charts.sharePlatformChart.resize();
    } else if (type === 'share-status' && StatisticsState.charts.shareStatusChart) {
      StatisticsState.charts.shareStatusChart.resize();
    }
  });
});
