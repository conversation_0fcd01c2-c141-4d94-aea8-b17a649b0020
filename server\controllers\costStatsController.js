const { pool } = require('../config/db');
const moment = require('moment');

// 价格常量配置
const PRICES = {
  MODEL_INPUT: 0.0024, // 模型调用-输入：¥0.0024 / 千Token (非日运会话)，2025年6月25日前
  MODEL_OUTPUT: 0.0096,  // 模型调用-输出：¥0.0096 / 千Token (非日运会话)，2025年6月25日前
  MODEL_INPUT_NEW: 0.0008, // 模型调用-输入：¥0.0008 / 千Token (非日运会话)，2025年6月25日起至6月30日前
  MODEL_OUTPUT_NEW: 0.002, // 模型调用-输出：¥0.002 / 千Token (非日运会话)，2025年6月25日起至6月30日前
  MODEL_INPUT_JUNE30: 0.0024, // 模型调用-输入：¥0.0024 / 千Token (非日运会话)，2025年6月30日起
  MODEL_OUTPUT_JUNE30: 0.0096, // 模型调用-输出：¥0.0096 / 千Token (非日运会话)，2025年6月30日起
  MODEL_INPUT_JULY10: 0.0008, // 模型调用-输入：¥0.0008 / 千Token (非日运会话)，2025年7月10日起
  MODEL_OUTPUT_JULY10: 0.002, // 模型调用-输出：¥0.002 / 千Token (非日运会话)，2025年7月10日起
  DAILY_FORTUNE_INPUT: 0.0003, // 日运会话-输入：¥0.0003 / 千Token
  DAILY_FORTUNE_OUTPUT: 0.0006, // 日运会话-输出：¥0.0006 / 千Token
  ETHICAL_INPUT: 0.0003, // 伦理检查-输入：¥0.0003 / 千Token
  ETHICAL_OUTPUT: 0.0006, // 伦理检查-输出：¥0.0006 / 千Token
  SPREAD_RECOM_INPUT: 0.0003, // AI推荐牌阵-输入：¥0.0003 / 千Token（与安全检测一致）
  SPREAD_RECOM_OUTPUT: 0.0006, // AI推荐牌阵-输出：¥0.0006 / 千Token（与安全检测一致）
  HOROSCOPE_INPUT: 0.0008, // 星座运势-输入：¥0.0008 / 千Token
  HOROSCOPE_OUTPUT: 0.002,  // 星座运势-输出：¥0.002 / 千Token
  ANONYMOUS_INPUT: 0.0008, // 未登录用户-输入：¥0.0008 / 千Token
  ANONYMOUS_OUTPUT: 0.002  // 未登录用户-输出：¥0.002 / 千Token
};

// 判断日期是否在2025年6月25日之后
function isAfterPriceChange(dateStr) {
  const priceChangeDate = new Date('2025-06-25');
  const date = new Date(dateStr);
  return date >= priceChangeDate;
}

// 判断日期是否在2025年6月30日之后
function isAfterJune30(dateStr) {
  const june30Date = new Date('2025-06-30');
  const date = new Date(dateStr);
  return date >= june30Date;
}

// 判断日期是否在2025年7月10日之后
function isAfterJuly10(dateStr) {
  const july10Date = new Date('2025-07-10');
  const date = new Date(dateStr);
  return date >= july10Date;
}

// 根据日期获取相应的价格
function getModelInputPrice(dateStr) {
  if (isAfterJuly10(dateStr)) {
    return PRICES.MODEL_INPUT_JULY10;
  } else if (isAfterJune30(dateStr)) {
    return PRICES.MODEL_INPUT_JUNE30;
  }
  const price = isAfterPriceChange(dateStr) ? PRICES.MODEL_INPUT_NEW : PRICES.MODEL_INPUT;
  return price;
}

function getModelOutputPrice(dateStr) {
  if (isAfterJuly10(dateStr)) {
    return PRICES.MODEL_OUTPUT_JULY10;
  } else if (isAfterJune30(dateStr)) {
    return PRICES.MODEL_OUTPUT_JUNE30;
  }
  const price = isAfterPriceChange(dateStr) ? PRICES.MODEL_OUTPUT_NEW : PRICES.MODEL_OUTPUT;
  return price;
}

// 获取成本统计数据
async function getCostStats(req, res) {
  try {
    const { startDate, endDate } = req.query;
    const start = startDate || moment().subtract(30, 'days').format('YYYY-MM-DD');
    const end = endDate || moment().format('YYYY-MM-DD');

    // 生成日期范围
    const dateRange = [];
    let current = moment(start);
    const endMoment = moment(end);
    
    while (current.isSameOrBefore(endMoment)) {
      dateRange.push(current.format('YYYY-MM-DD'));
      current.add(1, 'days');
    }

    // 获取每日token使用量，区分日运会话和普通会话
    const [dailyTokens] = await pool.query(`
      SELECT 
        DATE(timestamp) as date,
        SUM(CASE WHEN spread_id = 'daily-fortune' THEN input_tokens ELSE 0 END) as daily_fortune_input_tokens,
        SUM(CASE WHEN spread_id = 'daily-fortune' THEN output_tokens ELSE 0 END) as daily_fortune_output_tokens,
        SUM(CASE WHEN spread_id != 'daily-fortune' OR spread_id IS NULL THEN input_tokens ELSE 0 END) as regular_input_tokens,
        SUM(CASE WHEN spread_id != 'daily-fortune' OR spread_id IS NULL THEN output_tokens ELSE 0 END) as regular_output_tokens,
        SUM(Ethical_input_token) as total_ethical_input_tokens,
        SUM(Ethical_output_token) as total_ethical_output_tokens,
        SUM(spread_recommendation_input_tokens) as total_spread_recom_input_tokens,
        SUM(spread_recommendation_output_tokens) as total_spread_recom_output_tokens,
        AVG(spread_recommendation_time) as avg_spread_recommendation_time,
        COUNT(*) as session_count
      FROM sessions
      WHERE timestamp BETWEEN ? AND ?
      GROUP BY DATE(timestamp)
      ORDER BY date
    `, [start + ' 00:00:00', end + ' 23:59:59']);

    // 获取每日TTS成本
    const [dailyTtsCost] = await pool.query(`
      SELECT 
        DATE(created_at) as date,
        SUM(cost) as total_tts_cost,
        SUM(character_count) as total_character_count,
        COUNT(*) as tts_count
      FROM tts_cache
      WHERE created_at BETWEEN ? AND ?
      GROUP BY DATE(created_at)
      ORDER BY date
    `, [start + ' 00:00:00', end + ' 23:59:59']);
    
    // 获取每日星座运势token使用量
    const [dailyHoroscopeTokens] = await pool.query(`
      SELECT
        DATE(generated_at) as date,
        SUM(input_tokens) as horoscope_input_tokens,
        SUM(output_tokens) as horoscope_output_tokens,
        COUNT(*) as horoscope_count
      FROM tarot_horoscopes
      WHERE generated_at BETWEEN ? AND ?
      GROUP BY DATE(generated_at)
      ORDER BY date
    `, [start + ' 00:00:00', end + ' 23:59:59']);

    // 获取每日匿名用户token使用量
    const [dailyAnonymousTokens] = await pool.query(`
      SELECT
        DATE(created_at) as date,
        SUM(input_tokens) as anonymous_input_tokens,
        SUM(output_tokens) as anonymous_output_tokens,
        COUNT(*) as anonymous_count
      FROM anonymous_divination_records
      WHERE created_at BETWEEN ? AND ?
        AND input_tokens IS NOT NULL
        AND output_tokens IS NOT NULL
      GROUP BY DATE(created_at)
      ORDER BY date
    `, [start + ' 00:00:00', end + ' 23:59:59']);

    // 初始化数据结构
    const dailyFortuneInputTokensData = {}; // 日运会话输入token
    const dailyFortuneOutputTokensData = {}; // 日运会话输出token
    const regularInputTokensData = {}; // 普通会话输入token
    const regularOutputTokensData = {}; // 普通会话输出token
    const modelInputTokensData = {}; // 总输入token (用于展示)
    const modelOutputTokensData = {}; // 总输出token (用于展示) 
    const ethicalInputTokensData = {};
    const ethicalOutputTokensData = {};
    const spreadRecomInputTokensData = {}; 
    const spreadRecomOutputTokensData = {}; 
    const spreadRecomTimeData = {}; 
    const horoscopeInputTokensData = {}; // 星座运势输入token
    const horoscopeOutputTokensData = {}; // 星座运势输出token
    const horoscopeCountData = {}; // 星座运势生成次数
    const anonymousInputTokensData = {}; // 匿名用户输入token
    const anonymousOutputTokensData = {}; // 匿名用户输出token
    const anonymousCountData = {}; // 匿名用户调用次数
    const modelInputCostData = {};
    const modelOutputCostData = {};
    const ethicalInputCostData = {};
    const ethicalOutputCostData = {};
    const spreadRecomInputCostData = {};
    const spreadRecomOutputCostData = {};
    const horoscopeInputCostData = {}; // 星座运势输入成本
    const horoscopeOutputCostData = {}; // 星座运势输出成本
    const horoscopeTotalCostData = {}; // 星座运势总成本
    const anonymousInputCostData = {}; // 匿名用户输入成本
    const anonymousOutputCostData = {}; // 匿名用户输出成本
    const anonymousTotalCostData = {}; // 匿名用户总成本
    const ttsCostData = {};
    const ttsCharacterCountData = {};
    const ttsCountData = {};
    const totalCostData = {};
    const sessionCountData = {};

    // 初始化日期范围内的数据
    dateRange.forEach(date => {
      dailyFortuneInputTokensData[date] = 0;
      dailyFortuneOutputTokensData[date] = 0;
      regularInputTokensData[date] = 0;
      regularOutputTokensData[date] = 0;
      modelInputTokensData[date] = 0;
      modelOutputTokensData[date] = 0;
      ethicalInputTokensData[date] = 0;
      ethicalOutputTokensData[date] = 0;
      spreadRecomInputTokensData[date] = 0; 
      spreadRecomOutputTokensData[date] = 0; 
      spreadRecomTimeData[date] = 0; 
      horoscopeInputTokensData[date] = 0; // 星座运势初始化
      horoscopeOutputTokensData[date] = 0; // 星座运势初始化
      horoscopeCountData[date] = 0; // 星座运势初始化
      anonymousInputTokensData[date] = 0; // 匿名用户初始化
      anonymousOutputTokensData[date] = 0; // 匿名用户初始化
      anonymousCountData[date] = 0; // 匿名用户初始化
      modelInputCostData[date] = 0;
      modelOutputCostData[date] = 0;
      ethicalInputCostData[date] = 0;
      ethicalOutputCostData[date] = 0;
      spreadRecomInputCostData[date] = 0;
      spreadRecomOutputCostData[date] = 0;
      horoscopeInputCostData[date] = 0; // 星座运势初始化
      horoscopeOutputCostData[date] = 0; // 星座运势初始化
      horoscopeTotalCostData[date] = 0; // 星座运势初始化
      anonymousInputCostData[date] = 0; // 匿名用户初始化
      anonymousOutputCostData[date] = 0; // 匿名用户初始化
      anonymousTotalCostData[date] = 0; // 匿名用户初始化
      ttsCostData[date] = 0;
      ttsCharacterCountData[date] = 0;
      ttsCountData[date] = 0;
      totalCostData[date] = 0;
      sessionCountData[date] = 0;
    });

    // 填充每天的token使用量和成本数据
    dailyTokens.forEach(row => {
      const dateStr = moment(row.date).format('YYYY-MM-DD');
      
      if (dateRange.includes(dateStr)) {
        // 设置token使用量
        dailyFortuneInputTokensData[dateStr] = row.daily_fortune_input_tokens || 0;
        dailyFortuneOutputTokensData[dateStr] = row.daily_fortune_output_tokens || 0;
        regularInputTokensData[dateStr] = row.regular_input_tokens || 0;
        regularOutputTokensData[dateStr] = row.regular_output_tokens || 0;
        
        // 总token数（用于展示）
        modelInputTokensData[dateStr] = (row.daily_fortune_input_tokens || 0) + (row.regular_input_tokens || 0);
        modelOutputTokensData[dateStr] = (row.daily_fortune_output_tokens || 0) + (row.regular_output_tokens || 0);
        
        ethicalInputTokensData[dateStr] = row.total_ethical_input_tokens || 0;
        ethicalOutputTokensData[dateStr] = row.total_ethical_output_tokens || 0;
        spreadRecomInputTokensData[dateStr] = row.total_spread_recom_input_tokens || 0; 
        spreadRecomOutputTokensData[dateStr] = row.total_spread_recom_output_tokens || 0;
        // 先将 avg_spread_recommendation_time 转换为数值类型
        const avgTime = parseFloat(row.avg_spread_recommendation_time) || 0;
        spreadRecomTimeData[dateStr] = parseFloat(avgTime.toFixed(2)); 
        sessionCountData[dateStr] = row.session_count || 0;
        
        // 计算成本（区分日运会话和普通会话的价格）
        const dailyFortuneInputCost = (row.daily_fortune_input_tokens || 0) * PRICES.DAILY_FORTUNE_INPUT / 1000;
        const dailyFortuneOutputCost = (row.daily_fortune_output_tokens || 0) * PRICES.DAILY_FORTUNE_OUTPUT / 1000;
        const regularInputCost = (row.regular_input_tokens || 0) * getModelInputPrice(dateStr) / 1000;
        const regularOutputCost = (row.regular_output_tokens || 0) * getModelOutputPrice(dateStr) / 1000;
        
        // 合并成模型调用输入/输出成本
        modelInputCostData[dateStr] = parseFloat((dailyFortuneInputCost + regularInputCost).toFixed(4));
        modelOutputCostData[dateStr] = parseFloat((dailyFortuneOutputCost + regularOutputCost).toFixed(4));
        
        const ethicalInputCost = (row.total_ethical_input_tokens || 0) * PRICES.ETHICAL_INPUT / 1000;
        const ethicalOutputCost = (row.total_ethical_output_tokens || 0) * PRICES.ETHICAL_OUTPUT / 1000;
        const spreadRecomInputCost = (row.total_spread_recom_input_tokens || 0) * PRICES.SPREAD_RECOM_INPUT / 1000; 
        const spreadRecomOutputCost = (row.total_spread_recom_output_tokens || 0) * PRICES.SPREAD_RECOM_OUTPUT / 1000;
        
        // 设置成本数据
        ethicalInputCostData[dateStr] = parseFloat(ethicalInputCost.toFixed(4));
        ethicalOutputCostData[dateStr] = parseFloat(ethicalOutputCost.toFixed(4));
        spreadRecomInputCostData[dateStr] = parseFloat(spreadRecomInputCost.toFixed(4)); 
        spreadRecomOutputCostData[dateStr] = parseFloat(spreadRecomOutputCost.toFixed(4)); 
      }
    });

    // 填充每天的星座运势token使用量和成本数据
    dailyHoroscopeTokens.forEach(row => {
      const dateStr = moment(row.date).format('YYYY-MM-DD');
      
      if (dateRange.includes(dateStr)) {
        // 设置星座运势token使用量
        horoscopeInputTokensData[dateStr] = row.horoscope_input_tokens || 0;
        horoscopeOutputTokensData[dateStr] = row.horoscope_output_tokens || 0;
        horoscopeCountData[dateStr] = row.horoscope_count || 0;
        
        // 计算星座运势成本
        const horoscopeInputCost = (row.horoscope_input_tokens || 0) * PRICES.HOROSCOPE_INPUT / 1000;
        const horoscopeOutputCost = (row.horoscope_output_tokens || 0) * PRICES.HOROSCOPE_OUTPUT / 1000;
        
        // 设置星座运势成本数据
        horoscopeInputCostData[dateStr] = parseFloat(horoscopeInputCost.toFixed(4));
        horoscopeOutputCostData[dateStr] = parseFloat(horoscopeOutputCost.toFixed(4));
        horoscopeTotalCostData[dateStr] = parseFloat((horoscopeInputCost + horoscopeOutputCost).toFixed(4));
      }
    });

    // 填充每天的匿名用户token使用量和成本数据
    dailyAnonymousTokens.forEach(row => {
      const dateStr = moment(row.date).format('YYYY-MM-DD');

      if (dateRange.includes(dateStr)) {
        // 设置匿名用户token使用量
        anonymousInputTokensData[dateStr] = row.anonymous_input_tokens || 0;
        anonymousOutputTokensData[dateStr] = row.anonymous_output_tokens || 0;
        anonymousCountData[dateStr] = row.anonymous_count || 0;

        // 计算匿名用户成本
        const anonymousInputCost = (row.anonymous_input_tokens || 0) * PRICES.ANONYMOUS_INPUT / 1000;
        const anonymousOutputCost = (row.anonymous_output_tokens || 0) * PRICES.ANONYMOUS_OUTPUT / 1000;

        // 设置匿名用户成本数据
        anonymousInputCostData[dateStr] = parseFloat(anonymousInputCost.toFixed(4));
        anonymousOutputCostData[dateStr] = parseFloat(anonymousOutputCost.toFixed(4));
        anonymousTotalCostData[dateStr] = parseFloat((anonymousInputCost + anonymousOutputCost).toFixed(4));
      }
    });

    // 填充每天的TTS成本数据
    dailyTtsCost.forEach(row => {
      const dateStr = moment(row.date).format('YYYY-MM-DD');
      
      if (dateRange.includes(dateStr)) {
        ttsCostData[dateStr] = parseFloat(row.total_tts_cost || 0);
        ttsCharacterCountData[dateStr] = row.total_character_count || 0;
        ttsCountData[dateStr] = row.tts_count || 0;
      }
    });

    // 计算每日总成本
    dateRange.forEach(date => {
      const modelCost = modelInputCostData[date] + modelOutputCostData[date];
      const ethicalCost = ethicalInputCostData[date] + ethicalOutputCostData[date];
      const spreadRecomCost = spreadRecomInputCostData[date] + spreadRecomOutputCostData[date];
      const ttsCost = ttsCostData[date];
      const horoscopeCost = horoscopeTotalCostData[date]; // 添加星座运势成本
      const anonymousCost = anonymousTotalCostData[date]; // 添加匿名用户成本
      totalCostData[date] = parseFloat((modelCost + ethicalCost + spreadRecomCost + ttsCost + horoscopeCost + anonymousCost).toFixed(4));
    });

    // 获取总计数据，区分日运会话和普通会话
    const [totalTokensResult] = await pool.query(`
      SELECT 
        SUM(CASE WHEN spread_id = 'daily-fortune' THEN input_tokens ELSE 0 END) as daily_fortune_input_tokens,
        SUM(CASE WHEN spread_id = 'daily-fortune' THEN output_tokens ELSE 0 END) as daily_fortune_output_tokens,
        SUM(CASE WHEN spread_id != 'daily-fortune' OR spread_id IS NULL THEN input_tokens ELSE 0 END) as regular_input_tokens,
        SUM(CASE WHEN spread_id != 'daily-fortune' OR spread_id IS NULL THEN output_tokens ELSE 0 END) as regular_output_tokens,
        SUM(Ethical_input_token) as total_ethical_input_tokens,
        SUM(Ethical_output_token) as total_ethical_output_tokens,
        SUM(spread_recommendation_input_tokens) as total_spread_recom_input_tokens,
        SUM(spread_recommendation_output_tokens) as total_spread_recom_output_tokens,
        AVG(spread_recommendation_time) as avg_spread_recommendation_time,
        COUNT(*) as total_sessions
      FROM sessions
      WHERE timestamp BETWEEN ? AND ?
    `, [start + ' 00:00:00', end + ' 23:59:59']);

    // 获取TTS总计数据
    const [totalTtsResult] = await pool.query(`
      SELECT 
        SUM(cost) as total_tts_cost,
        SUM(character_count) as total_character_count,
        COUNT(*) as total_tts_count
      FROM tts_cache
      WHERE created_at BETWEEN ? AND ?
    `, [start + ' 00:00:00', end + ' 23:59:59']);
    
    // 获取星座运势总计数据
    const [totalHoroscopeResult] = await pool.query(`
      SELECT
        SUM(input_tokens) as total_horoscope_input_tokens,
        SUM(output_tokens) as total_horoscope_output_tokens,
        COUNT(*) as total_horoscope_count
      FROM tarot_horoscopes
      WHERE generated_at BETWEEN ? AND ?
    `, [start + ' 00:00:00', end + ' 23:59:59']);

    // 获取匿名用户总计数据
    const [totalAnonymousResult] = await pool.query(`
      SELECT
        SUM(input_tokens) as total_anonymous_input_tokens,
        SUM(output_tokens) as total_anonymous_output_tokens,
        COUNT(*) as total_anonymous_count
      FROM anonymous_divination_records
      WHERE created_at BETWEEN ? AND ?
        AND input_tokens IS NOT NULL
        AND output_tokens IS NOT NULL
    `, [start + ' 00:00:00', end + ' 23:59:59']);

    const totalData = totalTokensResult[0] || {
      daily_fortune_input_tokens: 0,
      daily_fortune_output_tokens: 0,
      regular_input_tokens: 0,
      regular_output_tokens: 0,
      total_ethical_input_tokens: 0,
      total_ethical_output_tokens: 0,
      total_spread_recom_input_tokens: 0,
      total_spread_recom_output_tokens: 0,
      avg_spread_recommendation_time: 0,
      total_sessions: 0
    };

    const totalTtsData = totalTtsResult[0] || {
      total_tts_cost: 0,
      total_character_count: 0,
      total_tts_count: 0
    };
    
    const totalHoroscopeData = totalHoroscopeResult[0] || {
      total_horoscope_input_tokens: 0,
      total_horoscope_output_tokens: 0,
      total_horoscope_count: 0
    };

    const totalAnonymousData = totalAnonymousResult[0] || {
      total_anonymous_input_tokens: 0,
      total_anonymous_output_tokens: 0,
      total_anonymous_count: 0
    };

    // 计算总成本（区分日运会话和普通会话的价格）
    const totalDailyFortuneInputCost = (totalData.daily_fortune_input_tokens || 0) * PRICES.DAILY_FORTUNE_INPUT / 1000;
    const totalDailyFortuneOutputCost = (totalData.daily_fortune_output_tokens || 0) * PRICES.DAILY_FORTUNE_OUTPUT / 1000;
    const totalRegularInputCost = (totalData.regular_input_tokens || 0) * getModelInputPrice(end) / 1000;
    const totalRegularOutputCost = (totalData.regular_output_tokens || 0) * getModelOutputPrice(end) / 1000;
    
    const totalModelInputCost = totalDailyFortuneInputCost + totalRegularInputCost;
    const totalModelOutputCost = totalDailyFortuneOutputCost + totalRegularOutputCost;
    const totalModelCost = totalModelInputCost + totalModelOutputCost;
    
    const totalEthicalInputCost = (totalData.total_ethical_input_tokens || 0) * PRICES.ETHICAL_INPUT / 1000;
    const totalEthicalOutputCost = (totalData.total_ethical_output_tokens || 0) * PRICES.ETHICAL_OUTPUT / 1000;
    const totalEthicalCost = totalEthicalInputCost + totalEthicalOutputCost;
    
    const totalSpreadRecomInputCost = (totalData.total_spread_recom_input_tokens || 0) * PRICES.SPREAD_RECOM_INPUT / 1000;
    const totalSpreadRecomOutputCost = (totalData.total_spread_recom_output_tokens || 0) * PRICES.SPREAD_RECOM_OUTPUT / 1000;
    const totalSpreadRecomCost = totalSpreadRecomInputCost + totalSpreadRecomOutputCost;
    
    // 计算星座运势总成本
    const totalHoroscopeInputCost = (totalHoroscopeData.total_horoscope_input_tokens || 0) * PRICES.HOROSCOPE_INPUT / 1000;
    const totalHoroscopeOutputCost = (totalHoroscopeData.total_horoscope_output_tokens || 0) * PRICES.HOROSCOPE_OUTPUT / 1000;
    const totalHoroscopeCost = totalHoroscopeInputCost + totalHoroscopeOutputCost;

    // 计算匿名用户总成本
    const totalAnonymousInputCost = (totalAnonymousData.total_anonymous_input_tokens || 0) * PRICES.ANONYMOUS_INPUT / 1000;
    const totalAnonymousOutputCost = (totalAnonymousData.total_anonymous_output_tokens || 0) * PRICES.ANONYMOUS_OUTPUT / 1000;
    const totalAnonymousCost = totalAnonymousInputCost + totalAnonymousOutputCost;

    const totalTtsCost = parseFloat(totalTtsData.total_tts_cost || 0);
    const totalCost = totalModelCost + totalEthicalCost + totalSpreadRecomCost + totalTtsCost + totalHoroscopeCost + totalAnonymousCost; // 添加匿名用户成本
    
    // 计算平均会话成本
    const avgCost = totalData.total_sessions > 0 ? totalCost / totalData.total_sessions : 0;

    // 返回数据
    res.json({
      success: true,
      data: {
        dateRange,
        modelInputTokensData,
        modelOutputTokensData,
        // ethicalInputTokensData,     // 注释掉伦理检查数据
        // ethicalOutputTokensData,    // 注释掉伦理检查数据
        spreadRecomInputTokensData,
        spreadRecomOutputTokensData,
        spreadRecomTimeData,
        horoscopeInputTokensData,  // 添加星座运势数据
        horoscopeOutputTokensData, // 添加星座运势数据
        horoscopeCountData,        // 添加星座运势数据
        anonymousInputTokensData,  // 添加匿名用户数据
        anonymousOutputTokensData, // 添加匿名用户数据
        anonymousCountData,        // 添加匿名用户数据
        modelInputCostData,
        modelOutputCostData,
        // ethicalInputCostData,       // 注释掉伦理检查成本数据
        // ethicalOutputCostData,      // 注释掉伦理检查成本数据
        spreadRecomInputCostData,
        spreadRecomOutputCostData,
        horoscopeInputCostData,    // 添加星座运势成本数据
        horoscopeOutputCostData,   // 添加星座运势成本数据
        horoscopeTotalCostData,    // 添加星座运势总成本数据
        anonymousInputCostData,    // 添加匿名用户输入成本数据
        anonymousOutputCostData,   // 添加匿名用户输出成本数据
        anonymousTotalCostData,    // 添加匿名用户总成本数据
        ttsCostData,
        ttsCharacterCountData,
        ttsCountData,
        totalCostData,
        sessionCountData,
        summary: {
          totalModelInputTokens: (totalData.daily_fortune_input_tokens || 0) + (totalData.regular_input_tokens || 0),
          totalModelOutputTokens: (totalData.daily_fortune_output_tokens || 0) + (totalData.regular_output_tokens || 0),
          totalDailyFortuneInputTokens: totalData.daily_fortune_input_tokens || 0,
          totalDailyFortuneOutputTokens: totalData.daily_fortune_output_tokens || 0,
          totalRegularInputTokens: totalData.regular_input_tokens || 0,
          totalRegularOutputTokens: totalData.regular_output_tokens || 0,
          // totalEthicalInputTokens: totalData.total_ethical_input_tokens || 0,     // 注释掉伦理检查数据
          // totalEthicalOutputTokens: totalData.total_ethical_output_tokens || 0,   // 注释掉伦理检查数据
          totalSpreadRecomInputTokens: totalData.total_spread_recom_input_tokens || 0,
          totalSpreadRecomOutputTokens: totalData.total_spread_recom_output_tokens || 0,
          totalHoroscopeInputTokens: totalHoroscopeData.total_horoscope_input_tokens || 0,  // 添加星座运势token总数
          totalHoroscopeOutputTokens: totalHoroscopeData.total_horoscope_output_tokens || 0, // 添加星座运势token总数
          totalHoroscopeCount: totalHoroscopeData.total_horoscope_count || 0, // 添加星座运势总生成次数
          totalAnonymousInputTokens: totalAnonymousData.total_anonymous_input_tokens || 0,  // 添加匿名用户token总数
          totalAnonymousOutputTokens: totalAnonymousData.total_anonymous_output_tokens || 0, // 添加匿名用户token总数
          totalAnonymousCount: totalAnonymousData.total_anonymous_count || 0, // 添加匿名用户总调用次数
          avgSpreadRecomTime: parseFloat((parseFloat(totalData.avg_spread_recommendation_time) || 0).toFixed(2)),
          totalModelInputCost: parseFloat(totalModelInputCost.toFixed(4)),
          totalModelOutputCost: parseFloat(totalModelOutputCost.toFixed(4)),
          totalDailyFortuneInputCost: parseFloat(totalDailyFortuneInputCost.toFixed(4)),
          totalDailyFortuneOutputCost: parseFloat(totalDailyFortuneOutputCost.toFixed(4)),
          totalRegularInputCost: parseFloat(totalRegularInputCost.toFixed(4)),
          totalRegularOutputCost: parseFloat(totalRegularOutputCost.toFixed(4)),
          // totalEthicalInputCost: parseFloat(totalEthicalInputCost.toFixed(4)),      // 注释掉伦理检查成本
          // totalEthicalOutputCost: parseFloat(totalEthicalOutputCost.toFixed(4)),     // 注释掉伦理检查成本
          totalSpreadRecomInputCost: parseFloat(totalSpreadRecomInputCost.toFixed(4)),
          totalSpreadRecomOutputCost: parseFloat(totalSpreadRecomOutputCost.toFixed(4)),
          totalHoroscopeInputCost: parseFloat(totalHoroscopeInputCost.toFixed(4)),  // 添加星座运势输入成本
          totalHoroscopeOutputCost: parseFloat(totalHoroscopeOutputCost.toFixed(4)), // 添加星座运势输出成本
          totalHoroscopeCost: parseFloat(totalHoroscopeCost.toFixed(4)),            // 添加星座运势总成本
          totalAnonymousInputCost: parseFloat(totalAnonymousInputCost.toFixed(4)),  // 添加匿名用户输入成本
          totalAnonymousOutputCost: parseFloat(totalAnonymousOutputCost.toFixed(4)), // 添加匿名用户输出成本
          totalAnonymousCost: parseFloat(totalAnonymousCost.toFixed(4)),            // 添加匿名用户总成本
          totalModelCost: parseFloat(totalModelCost.toFixed(4)),
          // totalEthicalCost: parseFloat(totalEthicalCost.toFixed(4)),               // 注释掉伦理检查总成本
          totalSpreadRecomCost: parseFloat(totalSpreadRecomCost.toFixed(4)),
          totalTtsCost: parseFloat(totalTtsCost.toFixed(4)),
          totalCharacterCount: totalTtsData.total_character_count || 0,
          totalTtsCount: totalTtsData.total_tts_count || 0,
          totalCost: parseFloat(totalCost.toFixed(4)),
          totalSessions: totalData.total_sessions,
          avgCost: parseFloat(avgCost.toFixed(4))
        }
      }
    });
  } catch (error) {
    console.error('获取成本统计数据失败:', error);
    res.status(500).json({
      success: false,
      message: '获取成本统计数据失败',
      error: error.message
    });
  }
}

module.exports = {
  getCostStats
}; 