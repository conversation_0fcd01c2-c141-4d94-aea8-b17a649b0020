const { pool } = require('../config/db');
const moment = require('moment');

// 汇率配置 - 可以根据实际情况调整或使用API获取实时汇率
const EXCHANGE_RATES = {
  USD: 7.2, // 1 USD = 7.2 CNY (仅作为示例，实际应使用当前汇率)
  CNY: 1,
  RMB: 1
};

/**
 * 获取收入统计数据
 */
async function getIncomeStats(req, res) {
  try {
    const { startDate, endDate } = req.query;
    
    if (!startDate || !endDate) {
      return res.status(400).json({
        success: false,
        message: '缺少必要的日期参数'
      });
    }
    
    // 验证日期格式
    const start = moment(startDate, 'YYYY-MM-DD');
    const end = moment(endDate, 'YYYY-MM-DD');
    
    if (!start.isValid() || !end.isValid()) {
      return res.status(400).json({
        success: false,
        message: '日期格式无效'
      });
    }
    
    // 生成日期范围
    const dateRange = [];
    const current = moment(start);
    while (current <= end) {
      dateRange.push(current.format('YYYY-MM-DD'));
      current.add(1, 'day');
    }
    
    // 初始化数据结构
    const totalIncomeData = {};
    const wechatIncomeData = {};
    const alipayIncomeData = {};
    const paypalIncomeData = {};
    const orderCountData = {};
    
    // 按货币分类的收入数据
    const currencyIncomeData = {
      CNY: 0,
      USD: 0
    };
    
    dateRange.forEach(date => {
      totalIncomeData[date] = 0;
      wechatIncomeData[date] = 0;
      alipayIncomeData[date] = 0;
      paypalIncomeData[date] = 0;
      orderCountData[date] = 0;
    });
    
    console.log(`[收入统计] 查询日期范围: ${startDate} 至 ${endDate}`);
    
    // 修改查询，使用COALESCE函数，当trade_time为NULL时使用created_at，并获取货币信息
    const dailyIncomeQuery = `
      SELECT 
        DATE(COALESCE(trade_time, created_at)) as date,
        SUM(amount) as total_amount,
        COUNT(*) as order_count,
        payment_method,
        currency
      FROM payment_orders
      WHERE status = 'success'
      AND (
        (trade_time BETWEEN ? AND ?) OR 
        (trade_time IS NULL AND created_at BETWEEN ? AND ?)
      )
      GROUP BY DATE(COALESCE(trade_time, created_at)), payment_method, currency
    `;
    
    const [dailyIncome] = await pool.query(dailyIncomeQuery, [
      `${startDate} 00:00:00`,
      `${endDate} 23:59:59`,
      `${startDate} 00:00:00`,
      `${endDate} 23:59:59`
    ]);
    
    console.log(`[收入统计] 查询结果条数: ${dailyIncome.length}`);
    console.log('[收入统计] 查询结果详情:', JSON.stringify(dailyIncome, null, 2));
    
    // 单独查询PayPal订单
    const paypalQuery = `
      SELECT 
        id, order_id, amount, status, trade_time, created_at, payment_method, currency
      FROM payment_orders
      WHERE payment_method = 'paypal'
      AND status = 'success'
      AND (
        (trade_time BETWEEN ? AND ?) OR 
        (trade_time IS NULL AND created_at BETWEEN ? AND ?)
      )
    `;
    
    const [paypalOrders] = await pool.query(paypalQuery, [
      `${startDate} 00:00:00`,
      `${endDate} 23:59:59`,
      `${startDate} 00:00:00`,
      `${endDate} 23:59:59`
    ]);
    
    console.log(`[收入统计] PayPal订单查询结果: ${paypalOrders.length}条`);
    console.log('[收入统计] PayPal订单详情:', JSON.stringify(paypalOrders, null, 2));
    
    // 填充每天的收入数据
    dailyIncome.forEach(row => {
      const dateStr = moment(row.date).format('YYYY-MM-DD');
      
      console.log(`[收入统计] 处理日期 ${dateStr} 的 ${row.payment_method} 支付数据:`, row);
      
      if (dateRange.includes(dateStr)) {
        // 获取货币和金额
        const currency = row.currency || 'CNY'; // 默认为CNY
        const amount = parseFloat(row.total_amount || 0);
        const orderCount = parseInt(row.order_count || 0);
        
        // 获取汇率，如果没有对应的汇率，默认为1
        const exchangeRate = EXCHANGE_RATES[currency] || 1;
        
        // 转换为人民币金额
        const cnyAmount = amount * exchangeRate;
        
        // 累加到对应货币的总收入
        if (currencyIncomeData[currency] !== undefined) {
          currencyIncomeData[currency] += amount;
        } else {
          currencyIncomeData[currency] = amount;
        }
        
        // 累加总收入（转换为人民币）
        totalIncomeData[dateStr] += cnyAmount;
        orderCountData[dateStr] += orderCount;
        
        // 按支付方式分类收入
        if (row.payment_method === 'wechat') {
          wechatIncomeData[dateStr] += cnyAmount;
        } else if (row.payment_method === 'alipay') {
          alipayIncomeData[dateStr] += cnyAmount;
        } else if (row.payment_method === 'paypal') {
          paypalIncomeData[dateStr] += cnyAmount;
        } else {
          console.log(`[收入统计] 未知的支付方式: ${row.payment_method}`);
        }
      }
    });
    
    // 检查是否有大小写不一致的paypal记录
    const caseCheckQuery = `
      SELECT 
        payment_method, COUNT(*) as count
      FROM payment_orders
      WHERE payment_method LIKE '%paypal%'
      GROUP BY payment_method
    `;
    
    const [caseCheckResult] = await pool.query(caseCheckQuery);
    console.log('[收入统计] PayPal大小写检查结果:', JSON.stringify(caseCheckResult, null, 2));
    
    // 计算总收入
    const totalIncome = Object.values(totalIncomeData).reduce((sum, val) => sum + val, 0);
    const wechatIncome = Object.values(wechatIncomeData).reduce((sum, val) => sum + val, 0);
    const alipayIncome = Object.values(alipayIncomeData).reduce((sum, val) => sum + val, 0);
    const paypalIncome = Object.values(paypalIncomeData).reduce((sum, val) => sum + val, 0);
    const orderCount = Object.values(orderCountData).reduce((sum, val) => sum + val, 0);
    
    console.log('[收入统计] 计算结果摘要:', {
      totalIncome,
      wechatIncome,
      alipayIncome,
      paypalIncome,
      orderCount,
      currencyIncomeData
    });
    
    // 查询支付产品分布 - 同样修改为支持trade_time为NULL的情况，并获取货币信息
    const productDistributionQuery = `
      SELECT 
        product_id,
        product_name,
        COUNT(*) as order_count,
        SUM(amount) as total_amount,
        currency,
        payment_method
      FROM payment_orders
      WHERE status = 'success'
      AND (
        (trade_time BETWEEN ? AND ?) OR 
        (trade_time IS NULL AND created_at BETWEEN ? AND ?)
      )
      GROUP BY product_id, product_name, currency, payment_method
      ORDER BY total_amount DESC
    `;
    
    const [productDistribution] = await pool.query(productDistributionQuery, [
      `${startDate} 00:00:00`,
      `${endDate} 23:59:59`,
      `${startDate} 00:00:00`,
      `${endDate} 23:59:59`
    ]);
    
    // 处理产品分布数据，转换不同货币
    const processedProductDistribution = productDistribution.map(item => {
      const currency = item.currency || 'CNY';
      const exchangeRate = EXCHANGE_RATES[currency] || 1;
      
      return {
        ...item,
        original_amount: item.total_amount,
        original_currency: currency,
        total_amount: parseFloat(item.total_amount) * exchangeRate
      };
    });
    
    // 返回结果
    res.json({
      success: true,
      data: {
        dateRange,
        totalIncomeData,
        wechatIncomeData,
        alipayIncomeData,
        paypalIncomeData,
        orderCountData,
        productDistribution: processedProductDistribution,
        summary: {
          totalIncome,
          wechatIncome,
          alipayIncome,
          paypalIncome,
          orderCount
        }
      }
    });
  } catch (error) {
    console.error('获取收入统计数据失败:', error);
    res.status(500).json({
      success: false,
      message: '获取收入统计数据失败: ' + error.message
    });
  }
}

module.exports = {
  getIncomeStats
}; 