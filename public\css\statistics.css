/* 通用输入框加粗样式 */
input[type="text"],
input[type="search"],
input[type="email"],
input[type="password"],
input[type="number"],
input[type="date"],
input[type="datetime-local"],
select,
textarea {
  font-weight: 600 !important;
  color: #2c3e50 !important;
}

/* 输入框焦点状态 */
input[type="text"]:focus,
input[type="search"]:focus,
input[type="email"]:focus,
input[type="password"]:focus,
input[type="number"]:focus,
input[type="date"]:focus,
input[type="datetime-local"]:focus,
select:focus,
textarea:focus {
  outline: none;
  border-color: #3498db;
  box-shadow: 0 0 0 2px rgba(52, 152, 219, 0.2);
  font-weight: 700 !important;
}

body {
  font-family: 'Arial', sans-serif;
  margin: 0;
  padding: 20px;
  background-color: #f5f7fa;
  color: #333;
}
.container {
  max-width: 1200px;
  margin: 0 auto;
  background-color: white;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}
header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 15px;
  border-bottom: 1px solid #eee;
}
h1 {
  margin: 0;
  color: #2c3e50;
}
.date-filter {
  display: flex;
  align-items: center;
  gap: 15px;
}
.date-input {
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  min-width: 200px;
  font-weight: 600;
  color: #2c3e50;
}
.btn-refresh {
  background-color: #3498db;
  color: white;
  border: none;
  padding: 8px 15px;
  border-radius: 4px;
  cursor: pointer;
  transition: background-color 0.3s;
}
.btn-refresh:hover {
  background-color: #2980b9;
}
.chart-container {
  margin-top: 25px;
  padding: 15px;
  border-radius: 8px;
  background-color: white;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}
.chart-wrapper {
  position: relative;
  height: 400px;
  margin-bottom: 30px;
}
.chart-title {
  margin: 10px 0;
  color: #2c3e50;
  font-weight: 500;
}
.page-selector {
  margin: 20px 0;
}
.page-selector select {
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  width: 100%;
  max-width: 400px;
  font-weight: 600;
  color: #2c3e50;
}
.stats-summary {
  display: flex;
  gap: 20px;
  margin-bottom: 20px;
  flex-wrap: wrap;
}
.stat-card {
  flex: 1;
  min-width: 200px;
  padding: 15px;
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}
.stat-card.pv {
  background-color: #e8f4fd;
  border-left: 4px solid #3498db;
}
.stat-card.uv {
  background-color: #e8f9f0;
  border-left: 4px solid #2ecc71;
}
.stat-card.pv-ctr {
  background-color: #fff5e6;
  border-left: 4px solid #f39c12;
}
.stat-card.uv-ctr {
  background-color: #f9e8e8;
  border-left: 4px solid #e74c3c;
}
.stat-value {
  font-size: 24px;
  font-weight: bold;
}
.loading {
  text-align: center;
  padding: 50px;
  font-size: 18px;
  color: #7f8c8d;
}
.chart-tabs {
  display: flex;
  margin-bottom: 15px;
  border-bottom: 1px solid #eee;
}
.chart-tab {
  padding: 10px 20px;
  cursor: pointer;
  margin-right: 5px;
  border-radius: 4px 4px 0 0;
  background-color: #f8f9fa;
}
.chart-tab.active {
  background-color: #3498db;
  color: white;
}
.error-message {
  color: #e74c3c;
  background-color: #fad7d3;
  padding: 10px;
  border-radius: 5px;
  margin: 10px 0;
  display: none;
}
.data-type-tabs {
  display: flex;
  margin-bottom: 15px;
  border-bottom: 1px solid #eee;
}
.data-type-tab {
  padding: 12px 24px;
  cursor: pointer;
  margin-right: 5px;
  border-radius: 4px 4px 0 0;
  background-color: #f8f9fa;
  font-weight: 500;
  transition: all 0.2s ease;
}
.data-type-tab.active {
  background-color: #34495e;
  color: white;
}
.data-type-tab:hover:not(.active) {
  background-color: #e9ecef;
}
.stat-card.user-total {
  background-color: #e8f4fd;
  border-left: 4px solid #3498db;
}
.stat-card.user-normal {
  background-color: #e8f9f0;
  border-left: 4px solid #2ecc71;
}
.stat-card.user-vip {
  background-color: #fff5e6;
  border-left: 4px solid #f39c12;
}
.stat-card.user-new {
  background-color: #f9e8e8;
  border-left: 4px solid #e74c3c;
}

/* 会话统计卡片样式 */
.stat-card.session-total {
  background-color: #e8f4fd;
  border-left: 4px solid #3498db;
}
.stat-card.session-new {
  background-color: #e8f9f0;
  border-left: 4px solid #2ecc71;
}
.stat-card.session-pending {
  background-color: #fff5e6;
  border-left: 4px solid #f39c12;
}
.stat-card.session-completed {
  background-color: #f9e8e8;
  border-left: 4px solid #e74c3c;
}
.stat-card.session-security {
  background-color: #f0e7f7;
  border-left: 4px solid #9b59b6;
}
.stat-card.session-newline {
  background-color: #e0f7fa;
  border-left: 4px solid #00bcd4;
}
.stat-card.session-paragraph {
  background-color: #fff5e6;
  border-left: 4px solid #f39c12;
}
.stat-card.session-daily-fortune {
  background-color: #e8f9f0;
  border-left: 4px solid #2ecc71;
}
.stat-card.session-divination {
  background-color: #fff8e1;
  border-left: 4px solid #e67e22;
}

/* 成本统计卡片样式 */
.stat-card.cost-total {
  background-color: #f9e8e8;
  border-left: 4px solid #e74c3c;
}
.stat-card.cost-model {
  background-color: #e8f4fd;
  border-left: 4px solid #3498db;
}
.stat-card.cost-daily-fortune {
  background-color: #f5e7ff;
  border-left: 4px solid #a55eea;
}
.stat-card.cost-ethical {
  background-color: #f0e7f7;
  border-left: 4px solid #9b59b6;
}
.stat-card.cost-spread-recom {
  background-color: #ffe9f4;
  border-left: 4px solid #e84393;
}
.stat-card.cost-tts {
  background-color: #e8f9f0;
  border-left: 4px solid #2ecc71;
}
.stat-card.cost-avg {
  background-color: #fff5e6;
  border-left: 4px solid #f39c12;
}

/* 价格信息样式 */
.stat-price {
  font-size: 12px;
  color: #666;
  margin-top: 8px;
  padding-top: 8px;
  border-top: 1px dashed #ddd;
}

/* 日记样式 */
.diary-controls {
  margin-bottom: 20px;
  display: flex;
  justify-content: flex-end;
}

.btn-add-diary {
  background-color: #2ecc71;
  color: white;
  border: none;
  padding: 10px 20px;
  border-radius: 4px;
  cursor: pointer;
  font-weight: 500;
  transition: background-color 0.3s;
}

.btn-add-diary:hover {
  background-color: #27ae60;
}

.diary-list {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.diary-card {
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  padding: 20px;
  position: relative;
}

.diary-header {
  margin-bottom: 15px;
  border-bottom: 1px solid #eee;
  padding-bottom: 15px;
}

.diary-title {
  font-size: 18px;
  font-weight: 500;
  color: #2c3e50;
  margin: 0 0 10px 0;
}

.diary-date {
  color: #7f8c8d;
  font-size: 14px;
  margin-top: 5px;
}

.diary-content {
  font-size: 15px;
  line-height: 1.6;
  color: #34495e;
  white-space: pre-wrap;
}



.diary-form .form-group {
  margin-bottom: 15px;
}

.diary-form label {
  display: block;
  margin-bottom: 5px;
  font-weight: 500;
  color: #2c3e50;
}

.diary-form .form-control {
  width: 100%;
  padding: 10px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 15px;
  font-weight: 600;
  color: #2c3e50;
}

.diary-form textarea.form-control {
  resize: vertical;
}

.form-actions {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
  margin-top: 20px;
}

.btn-save {
  background-color: #3498db;
  color: white;
  border: none;
  padding: 8px 15px;
  border-radius: 4px;
  cursor: pointer;
}

.btn-cancel {
  background-color: #f8f9fa;
  color: #333;
  border: 1px solid #ddd;
  padding: 8px 15px;
  border-radius: 4px;
  cursor: pointer;
}

.btn-save:hover {
  background-color: #2980b9;
}

.btn-cancel:hover {
  background-color: #e9ecef;
}

.no-data {
  text-align: center;
  padding: 40px 0;
  color: #7f8c8d;
  font-size: 16px;
  background-color: #f8f9fa;
  border-radius: 8px;
  margin: 20px 0;
}

/* 收入统计卡片样式 */
.stat-card.income-total {
  background-color: #e8f9f0;
  border-left: 4px solid #2ecc71;
}
.stat-card.income-wechat {
  background-color: #e8f4fd;
  border-left: 4px solid #3498db;
}
.stat-card.income-alipay {
  background-color: #e0f7fa;
  border-left: 4px solid #00bcd4;
}
.stat-card.income-paypal {
  background-color: #f0e7f7;
  border-left: 4px solid #9b59b6;
}
.stat-card.income-orders {
  background-color: #fff5e6;
  border-left: 4px solid #f39c12;
}

/* 分享数统计卡片样式 */
.stat-card.share-total {
  background-color: #e8f4fd;
  border-left: 4px solid #3498db;
}
.stat-card.share-pending {
  background-color: #fff5e6;
  border-left: 4px solid #f39c12;
}
.stat-card.share-approved {
  background-color: #e8f9f0;
  border-left: 4px solid #2ecc71;
}
.stat-card.share-rewarded {
  background-color: #f0e7f7;
  border-left: 4px solid #9b59b6;
}

/* 平台选择器样式 */
.platform-selector {
  margin-bottom: 20px;
  padding: 15px;
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.platform-selector select {
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  background-color: white;
  min-width: 150px;
  font-weight: 600;
  color: #2c3e50;
}

/* 会话明细样式 */
.session-details-container {
  margin-top: 30px;
  padding: 20px;
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.session-details-container h3 {
  margin-top: 0;
  margin-bottom: 15px;
  color: #2c3e50;
}

.session-details-controls {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.search-box {
  display: flex;
  align-items: center;
}

.search-box input {
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  margin-right: 5px;
  font-weight: 600;
  color: #2c3e50;
}

.search-box input:first-child {
  min-width: 250px;
}

.search-box input#session-id-search {
  width: 120px;
}

.search-box button {
  padding: 8px 15px;
  background-color: #3498db;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}

.filter-options {
  display: flex;
  align-items: center;
  gap: 15px;
}

.filter-options label {
  display: flex;
  align-items: center;
  cursor: pointer;
}

.filter-options input[type="checkbox"] {
  margin-right: 5px;
}

.status-select {
  padding: 6px 10px;
  border: 1px solid #ddd;
  border-radius: 4px;
  background-color: white;
  min-width: 120px;
  font-weight: 600;
  color: #2c3e50;
}

.session-details-table-container {
  max-height: 400px;
  overflow-y: auto;
  margin-bottom: 15px;
  border: 1px solid #eee;
}

.session-details-table {
  width: 100%;
  border-collapse: collapse;
}

.session-details-table th,
.session-details-table td {
  padding: 10px;
  text-align: left;
  border-bottom: 1px solid #eee;
}

.session-details-table th {
  background-color: #f8f9fa;
  position: sticky;
  top: 0;
  z-index: 10;
}

.session-details-table tbody tr:hover {
  background-color: #f5f7fa;
}

.session-details-table .question-text {
  max-width: 500px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.session-details-table .question-text.expanded {
  white-space: normal;
  overflow: visible;
}

.session-details-table .security-badge {
  display: inline-block;
  padding: 2px 6px;
  background-color: #9b59b6;
  color: white;
  border-radius: 4px;
  font-size: 12px;
}

.session-pagination {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 15px;
}

.pagination-btn {
  padding: 6px 12px;
  background-color: #f8f9fa;
  border: 1px solid #ddd;
  border-radius: 4px;
  cursor: pointer;
}

.pagination-btn:hover {
  background-color: #e9ecef;
}

.pagination-btn:disabled {
  background-color: #f8f9fa;
  color: #adb5bd;
  cursor: not-allowed;
}

@media (max-width: 768px) {
  .stats-summary {
    flex-direction: column;
  }
  
  .session-details-controls {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
  }
  
  .search-box input {
    min-width: 200px;
  }
}

/* 会话详情模态框样式 */
.modal {
  display: none;
  position: fixed;
  z-index: 1000;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  overflow: auto;
  background-color: rgba(0, 0, 0, 0.5);
}

.modal-content {
  position: relative;
  background-color: #fefefe;
  margin: 5% auto;
  padding: 0;
  border-radius: 8px;
  width: 80%;
  max-width: 900px;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
  animation: modalFadeIn 0.3s;
}

@keyframes modalFadeIn {
  from {opacity: 0; transform: translateY(-20px);}
  to {opacity: 1; transform: translateY(0);}
}

.modal-header {
  padding: 15px 20px;
  background-color: #34495e;
  color: white;
  border-radius: 8px 8px 0 0;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.modal-header h2 {
  margin: 0;
  font-size: 1.4rem;
}

.close-modal {
  color: white;
  font-size: 28px;
  font-weight: bold;
  cursor: pointer;
}

.close-modal:hover {
  color: #bdc3c7;
}

.modal-body {
  padding: 20px;
  max-height: 70vh;
  overflow-y: auto;
}

.session-detail-info {
  margin-bottom: 20px;
  padding-bottom: 15px;
  border-bottom: 1px solid #eee;
}

.detail-item {
  margin-bottom: 10px;
}

.detail-label {
  font-weight: bold;
  color: #555;
}

.detail-value {
  flex: 1;
  padding: 8px 0;
}

.detail-tabs {
  display: flex;
  border-bottom: 1px solid #ddd;
  margin-bottom: 15px;
}

.detail-tab {
  padding: 10px 20px;
  cursor: pointer;
  margin-right: 5px;
  border-radius: 4px 4px 0 0;
  background-color: #f8f9fa;
}

.detail-tab.active {
  background-color: #3498db;
  color: white;
}

.tab-content {
  display: none;
  padding: 15px;
  background-color: #f9f9f9;
  border-radius: 4px;
  min-height: 200px;
}

.tab-content.active {
  display: block;
}

.detail-text {
  white-space: pre-wrap;
  word-wrap: break-word;
  background-color: #f5f5f5;
  padding: 10px;
  border-radius: 4px;
  border: 1px solid #ddd;
  font-family: inherit;
  font-size: 14px;
  line-height: 1.5;
  color: #333;
  overflow-x: auto;
}

#dialog-history-content .dialog-item {
  margin-bottom: 15px;
  padding: 10px;
  border-radius: 4px;
}

#dialog-history-content .user-message {
  background-color: #e8f4fd;
  border-left: 4px solid #3498db;
}

#dialog-history-content .assistant-message {
  background-color: #e8f9f0;
  border-left: 4px solid #2ecc71;
}

#dialog-history-content .message-time {
  font-size: 0.8rem;
  color: #7f8c8d;
  margin-bottom: 5px;
}

#dialog-history-content .message-content {
  white-space: pre-wrap;
}

/* 会话详情表格中的问题文本点击样式 */
.question-text {
  max-width: 400px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  cursor: pointer;
  color: #3498db;
}

.question-text:hover {
  text-decoration: underline;
}

/* 塔罗解读样式 */
.reading-result-container {
  padding: 10px;
  background-color: #f5f5f5;
  border-radius: 4px;
  border: 1px solid #ddd;
}

.reading-section {
  margin-bottom: 20px;
}

.reading-title {
  font-weight: bold;
  color: #34495e;
  margin-bottom: 8px;
  padding-bottom: 5px;
  border-bottom: 1px solid #eee;
}

.reading-paragraph {
  margin-bottom: 15px;
  line-height: 1.6;
  white-space: pre-wrap;
  color: #333;
}

.reading-content {
  font-family: monospace;
  white-space: pre-wrap;
  background-color: #f8f9fa;
  padding: 8px;
  border-radius: 3px;
  border: 1px solid #eee;
  margin-top: 5px;
}

/* 安全检测信息样式 */
.security-info {
  margin: 15px 0;
  padding: 15px;
  background-color: #f0e7f7;
  border-radius: 6px;
  border-left: 4px solid #9b59b6;
}

.security-info h3 {
  margin-top: 0;
  color: #9b59b6;
  font-size: 16px;
  margin-bottom: 10px;
}

.security-info-item {
  margin-bottom: 10px;
  padding-bottom: 10px;
  border-bottom: 1px dashed #ddd;
}

.security-info-item:last-child {
  border-bottom: none;
  margin-bottom: 0;
  padding-bottom: 0;
}

.security-info-section {
  margin-bottom: 10px;
  background-color: rgba(255, 255, 255, 0.5);
  padding: 8px;
  border-radius: 4px;
}

.security-label {
  font-weight: bold;
  color: #555;
  margin-bottom: 5px;
  display: block;
}

.security-value {
  padding: 5px 0;
  line-height: 1.5;
}

.security-reason {
  background-color: #fff;
  padding: 8px;
  margin-bottom: 8px;
  border-radius: 4px;
  box-shadow: 0 1px 3px rgba(0,0,0,0.05);
}

.security-reason-list {
  margin: 5px 0 5px 20px;
  padding: 0;
}

.security-reason-list li {
  margin-bottom: 5px;
}

.no-data {
  color: #999;
  font-style: italic;
}

.security-tag {
  display: inline-block;
  background-color: #9b59b6;
  color: white;
  padding: 2px 8px;
  border-radius: 4px;
  margin: 2px;
  font-size: 12px;
}

/* 不同类型的安全检测会话样式 */
.ethical-intervention {
  border-left-color: #e74c3c;
  background-color: #fdeaea;
}

.potential-ethical-issue {
  border-left-color: #f39c12;
  background-color: #fef5e7;
}

.ethical-intervention-follow {
  border-left-color: #c0392b;
  background-color: #f9ebea;
}

.potential-ethical-issue-follow {
  border-left-color: #d35400;
  background-color: #fbeee6;
}

/* 辅助样式 */
.error {
  color: #e74c3c;
  padding: 5px;
  margin: 5px 0;
  background-color: #fad7d3;
  border-radius: 4px;
}

.mt-1 {
  margin-top: 5px;
}

.mt-2 {
  margin-top: 10px;
}

/* 货币明细样式已删除 */

.question-text.clickable {
  cursor: pointer;
  color: #3498db;
  text-decoration: underline;
}

.question-text.clickable:hover {
  color: #2980b9;
}

/* 对话历史中的安全检测信息样式 */
.message-security-info {
  margin-top: 8px;
  padding: 6px 8px;
  background-color: #f8f0ff;
  border-left: 2px solid #9b59b6;
  border-radius: 0;
  font-size: 0.9em;
  margin-bottom: 5px;
  box-shadow: none;
}

.dialog-item .security-info-section {
  margin-bottom: 6px;
  background-color: transparent;
  padding: 0;
  border-radius: 0;
}

.dialog-item .security-info-section:last-child {
  margin-bottom: 0;
}

.dialog-item .security-label {
  font-weight: bold;
  color: #555;
  margin-bottom: 2px;
  display: block;
  font-size: 12px;
}

.dialog-item .security-value {
  padding: 0;
  line-height: 1.4;
}

.dialog-item .security-tag {
  display: inline-block;
  background-color: #9b59b6;
  color: white;
  padding: 1px 6px;
  border-radius: 3px;
  margin: 2px;
  font-size: 11px;
}

.dialog-item .security-reason {
  background-color: transparent;
  padding: 3px 0;
  margin-bottom: 3px;
  border-radius: 0;
  font-size: 12px;
  border-left: none;
} 