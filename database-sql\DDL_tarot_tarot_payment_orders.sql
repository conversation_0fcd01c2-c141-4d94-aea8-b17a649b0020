/******************************************/
/*   DatabaseName = tarot   */
/*   TableName = payment_orders   */
/******************************************/
CREATE TABLE `payment_orders` (
  `id` varchar(36) NOT NULL,
  `order_id` varchar(36) NOT NULL,
  `user_id` varchar(36) NOT NULL,
  `product_id` varchar(50) NOT NULL,
  `product_name` varchar(255) NOT NULL,
  `amount` decimal(10,2) NOT NULL,
  `status` enum('pending','success','failed','closed') DEFAULT 'pending',
  `payment_method` enum('wechat','alipay','paypal') NOT NULL DEFAULT 'wechat',
  `trade_no` varchar(64) DEFAULT NULL,
  `trade_status` varchar(50) DEFAULT NULL,
  `trade_time` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `trade_state_desc` varchar(64) DEFAULT NULL,
  `trade_type` varchar(32) DEFAULT NULL,
  `bank_type` varchar(32) DEFAULT NULL,
  `payer_openid` varchar(128) DEFAULT NULL,
  `success_time` datetime DEFAULT NULL,
  `currency` varchar(16) DEFAULT NULL,
  `payer_currency` varchar(16) DEFAULT NULL,
  `buyer_logon_id` varchar(100) DEFAULT NULL COMMENT '买家支付宝账号',
  `buyer_user_id` varchar(100) DEFAULT NULL COMMENT '买家支付宝用户ID',
  `notify_id` varchar(128) DEFAULT NULL COMMENT '通知校验ID',
  `notify_time` datetime DEFAULT NULL COMMENT '通知时间',
  `gmt_create` datetime DEFAULT NULL COMMENT '交易创建时间',
  `gmt_payment` datetime DEFAULT NULL COMMENT '交易付款时间',
  `discount_applied` decimal(4,2) DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `order_id` (`order_id`),
  KEY `idx_order_id` (`order_id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_status` (`status`),
  KEY `idx_trade_no` (`trade_no`),
  KEY `idx_notify_id` (`notify_id`),
  KEY `idx_buyer_user_id` (`buyer_user_id`),
  CONSTRAINT `payment_orders_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3
;

