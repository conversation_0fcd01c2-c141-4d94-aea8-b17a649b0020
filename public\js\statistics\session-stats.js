// 初始化sessions图表
function initSessionCharts() {
  try {
    // 确保Canvas元素存在并可用
    function getCanvasContext(id) {
      const canvas = document.getElementById(id);
      if (!canvas) {
        throw new Error(`找不到Canvas元素: ${id}`);
      }
      return canvas.getContext('2d');
    }
    
    const ctxTotal = getCanvasContext('sessionTotalChart');
    const ctxNew = getCanvasContext('sessionNewChart');
    const ctxFormat = getCanvasContext('sessionFormatChart');
    
    // 销毁之前的图表实例
    if (StatisticsState.charts.sessionTotalChart) StatisticsState.charts.sessionTotalChart.destroy();
    if (StatisticsState.charts.sessionNewChart) StatisticsState.charts.sessionNewChart.destroy();
    if (StatisticsState.charts.sessionFormatChart) StatisticsState.charts.sessionFormatChart.destroy();
    
    // 创建累计sessions图表
    StatisticsState.charts.sessionTotalChart = new Chart(ctxTotal, {
      type: 'line',
      data: {
        labels: [],
        datasets: [
          {
            label: '总会话数（注册用户）',
            data: [],
            borderColor: '#3498db',
            backgroundColor: 'rgba(52, 152, 219, 0.1)',
            borderWidth: 2,
            tension: 0.1,
            fill: true
          },
          {
            label: '总匿名会话数',
            data: [],
            borderColor: '#e74c3c',
            backgroundColor: 'rgba(231, 76, 60, 0.1)',
            borderWidth: 2,
            tension: 0.1,
            fill: true
          },
          {
            label: '日运会话数（注册用户）',
            data: [],
            borderColor: '#9b59b6',
            backgroundColor: 'rgba(155, 89, 182, 0.1)',
            borderWidth: 2,
            tension: 0.1,
            fill: true
          },
          {
            label: '匿名日运会话数',
            data: [],
            borderColor: '#f39c12',
            backgroundColor: 'rgba(243, 156, 18, 0.1)',
            borderWidth: 2,
            tension: 0.1,
            fill: true
          },
          {
            label: '是非占卜会话数（注册用户）',
            data: [],
            borderColor: '#e67e22',
            backgroundColor: 'rgba(230, 126, 34, 0.1)',
            borderWidth: 2,
            tension: 0.1,
            fill: true
          },
          {
            label: '匿名是非占卜会话数',
            data: [],
            borderColor: '#27ae60',
            backgroundColor: 'rgba(39, 174, 96, 0.1)',
            borderWidth: 2,
            tension: 0.1,
            fill: true
          }
        ]
      },
      options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
          title: {
            display: false
          },
          tooltip: {
            mode: 'index',
            intersect: false
          }
        },
        scales: {
          x: {
            grid: {
              display: false
            }
          },
          y: {
            beginAtZero: true,
            ticks: {
              precision: 0
            }
          }
        }
      }
    });
    
    // 创建新增sessions图表
    StatisticsState.charts.sessionNewChart = new Chart(ctxNew, {
      type: 'line',
      data: {
        labels: [],
        datasets: [
          {
            label: '新增会话（注册用户）',
            data: [],
            borderColor: '#3498db',
            backgroundColor: 'rgba(52, 152, 219, 0.1)',
            borderWidth: 2,
            tension: 0.1,
            fill: true
          },
          {
            label: '新增匿名会话',
            data: [],
            borderColor: '#e74c3c',
            backgroundColor: 'rgba(231, 76, 60, 0.1)',
            borderWidth: 2,
            tension: 0.1,
            fill: true
          },
          {
            label: '已完成会话',
            data: [],
            borderColor: '#2ecc71',
            backgroundColor: 'rgba(46, 204, 113, 0.1)',
            borderWidth: 2,
            tension: 0.1,
            fill: true
          },
          {
            label: '新增日运会话（注册用户）',
            data: [],
            borderColor: '#9b59b6',
            backgroundColor: 'rgba(155, 89, 182, 0.1)',
            borderWidth: 2,
            tension: 0.1,
            fill: true
          },
          {
            label: '新增匿名日运会话',
            data: [],
            borderColor: '#f39c12',
            backgroundColor: 'rgba(243, 156, 18, 0.1)',
            borderWidth: 2,
            tension: 0.1,
            fill: true
          },
          {
            label: '新增是非占卜会话（注册用户）',
            data: [],
            borderColor: '#e67e22',
            backgroundColor: 'rgba(230, 126, 34, 0.1)',
            borderWidth: 2,
            tension: 0.1,
            fill: true
          },
          {
            label: '新增匿名是非占卜会话',
            data: [],
            borderColor: '#27ae60',
            backgroundColor: 'rgba(39, 174, 96, 0.1)',
            borderWidth: 2,
            tension: 0.1,
            fill: true
          }
        ]
      },
      options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
          title: {
            display: false
          },
          tooltip: {
            mode: 'index',
            intersect: false
          }
        },
        scales: {
          x: {
            grid: {
              display: false
            }
          },
          y: {
            beginAtZero: true,
            ticks: {
              precision: 0
            }
          }
        }
      }
    });
    
    // 创建格式统计图表
    StatisticsState.charts.sessionFormatChart = new Chart(ctxFormat, {
      type: 'line',
      data: {
        labels: [],
        datasets: [
          {
            label: '单行换行符',
            data: [],
            borderColor: '#00bcd4',
            backgroundColor: 'rgba(0, 188, 212, 0.1)',
            borderWidth: 2,
            tension: 0.1,
            fill: true
          },
          {
            label: '双行换行符',
            data: [],
            borderColor: '#03a9f4',
            backgroundColor: 'rgba(3, 169, 244, 0.1)',
            borderWidth: 2,
            tension: 0.1,
            fill: true
          },
          {
            label: '分段检测成功',
            data: [],
            borderColor: '#f39c12',
            backgroundColor: 'rgba(243, 156, 18, 0.1)',
            borderWidth: 2,
            tension: 0.1,
            fill: true
          },
          {
            label: '分段检测失败',
            data: [],
            borderColor: '#e74c3c',
            backgroundColor: 'rgba(231, 76, 60, 0.1)',
            borderWidth: 2,
            tension: 0.1,
            fill: true
          }
        ]
      },
      options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
          title: {
            display: false
          },
          tooltip: {
            mode: 'index',
            intersect: false
          }
        },
        scales: {
          x: {
            grid: {
              display: false
            }
          },
          y: {
            beginAtZero: true,
            ticks: {
              precision: 0
            }
          }
        }
      }
    });
    
    StatisticsState.flags.sessionChartsInitialized = true;
  } catch (error) {
    console.error('初始化会话图表出错:', error);
    showError('初始化会话图表失败: ' + error.message);
  }
}

// 格式化数字函数
function formatNumber(num) {
  return num.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ",");
}

// 更新sessions图表数据
function updateSessionCharts() {
  if (!StatisticsState.data.sessionData || !StatisticsState.flags.sessionChartsInitialized) {
    console.warn('无法更新会话图表：数据不完整或图表未初始化');
    return;
  }
  
  try {
    const {
      dateRange,
      totalSessionsData,
      newSessionsData,
      pendingSessionsData,
      completedSessionsData,
      singleNewlineData,
      doubleNewlineData,
      paragraphDetectionSuccessData,
      paragraphDetectionFailData,
      dailyFortuneSessionsData,
      divinationSessionsData,
      // 匿名会话数据
      totalAnonymousSessionsData,
      newAnonymousSessionsData,
      anonymousDailyFortuneSessionsData,
      anonymousDivinationSessionsData
    } = StatisticsState.data.sessionData;
    
    // 检查数据完整性
    if (!dateRange || !dateRange.length) {
      console.warn('日期范围数据不完整');
      return;
    }
    
    // 检查必要的数据对象是否存在
    const requiredData = [totalSessionsData, newSessionsData, completedSessionsData];
    if (requiredData.some(data => !data)) {
      console.warn('会话数据不完整');
      return;
    }
    
    // 计算累计日运会话数
    const cumulativeDailyFortuneData = {};
    let runningDailyFortuneTotal = 0;
    
    dateRange.forEach(date => {
      runningDailyFortuneTotal += dailyFortuneSessionsData[date] || 0;
      cumulativeDailyFortuneData[date] = runningDailyFortuneTotal;
    });
    
    // 计算累计是非占卜会话数
    const cumulativeDivinationData = {};
    let runningDivinationTotal = 0;

    dateRange.forEach(date => {
      runningDivinationTotal += divinationSessionsData[date] || 0;
      cumulativeDivinationData[date] = runningDivinationTotal;
    });

    // 计算累计匿名日运会话数
    const cumulativeAnonymousDailyFortuneData = {};
    let runningAnonymousDailyFortuneTotal = 0;

    dateRange.forEach(date => {
      runningAnonymousDailyFortuneTotal += anonymousDailyFortuneSessionsData[date] || 0;
      cumulativeAnonymousDailyFortuneData[date] = runningAnonymousDailyFortuneTotal;
    });

    // 计算累计匿名是非占卜会话数
    const cumulativeAnonymousDivinationData = {};
    let runningAnonymousDivinationTotal = 0;

    dateRange.forEach(date => {
      runningAnonymousDivinationTotal += anonymousDivinationSessionsData[date] || 0;
      cumulativeAnonymousDivinationData[date] = runningAnonymousDivinationTotal;
    });
    
    // 更新累计会话图表
    StatisticsState.charts.sessionTotalChart.data.labels = dateRange;
    StatisticsState.charts.sessionTotalChart.data.datasets[0].data = dateRange.map(date => totalSessionsData[date] || 0);
    StatisticsState.charts.sessionTotalChart.data.datasets[1].data = dateRange.map(date => totalAnonymousSessionsData[date] || 0);
    StatisticsState.charts.sessionTotalChart.data.datasets[2].data = dateRange.map(date => cumulativeDailyFortuneData[date] || 0);
    StatisticsState.charts.sessionTotalChart.data.datasets[3].data = dateRange.map(date => cumulativeAnonymousDailyFortuneData[date] || 0);
    StatisticsState.charts.sessionTotalChart.data.datasets[4].data = dateRange.map(date => cumulativeDivinationData[date] || 0);
    StatisticsState.charts.sessionTotalChart.data.datasets[5].data = dateRange.map(date => cumulativeAnonymousDivinationData[date] || 0);
    StatisticsState.charts.sessionTotalChart.update();
    
    // 更新新增会话图表
    StatisticsState.charts.sessionNewChart.data.labels = dateRange;
    StatisticsState.charts.sessionNewChart.data.datasets[0].data = dateRange.map(date => newSessionsData[date] || 0);
    StatisticsState.charts.sessionNewChart.data.datasets[1].data = dateRange.map(date => newAnonymousSessionsData[date] || 0);
    StatisticsState.charts.sessionNewChart.data.datasets[2].data = dateRange.map(date => completedSessionsData[date] || 0);
    StatisticsState.charts.sessionNewChart.data.datasets[3].data = dateRange.map(date => dailyFortuneSessionsData[date] || 0);
    StatisticsState.charts.sessionNewChart.data.datasets[4].data = dateRange.map(date => anonymousDailyFortuneSessionsData[date] || 0);
    StatisticsState.charts.sessionNewChart.data.datasets[5].data = dateRange.map(date => divinationSessionsData[date] || 0);
    StatisticsState.charts.sessionNewChart.data.datasets[6].data = dateRange.map(date => anonymousDivinationSessionsData[date] || 0);
    StatisticsState.charts.sessionNewChart.update();
    
    // 更新格式统计图表
    StatisticsState.charts.sessionFormatChart.data.labels = dateRange;
    StatisticsState.charts.sessionFormatChart.data.datasets[0].data = dateRange.map(date => singleNewlineData[date] || 0);
    StatisticsState.charts.sessionFormatChart.data.datasets[1].data = dateRange.map(date => doubleNewlineData[date] || 0);
    StatisticsState.charts.sessionFormatChart.data.datasets[2].data = dateRange.map(date => paragraphDetectionSuccessData[date] || 0);
    StatisticsState.charts.sessionFormatChart.data.datasets[3].data = dateRange.map(date => paragraphDetectionFailData[date] || 0);
    StatisticsState.charts.sessionFormatChart.update();
  } catch (error) {
    console.error('更新会话图表出错:', error);
    showError('更新会话图表失败: ' + error.message);
  }
}

// 更新sessions统计摘要
function updateSessionSummary() {
  if (!StatisticsState.data.sessionData) {
    console.warn('无法更新会话统计摘要：数据不完整');
    return;
  }
  
  try {
    const {
      dateRange,
      totalSessionsData,
      newSessionsData,
      completedSessionsData,
      singleNewlineData,
      doubleNewlineData,
      paragraphDetectionSuccessData,
      paragraphDetectionFailData,
      dailyFortuneSessionsData,
      divinationSessionsData,
      // 匿名会话数据
      newAnonymousSessionsData,
      anonymousDailyFortuneSessionsData,
      anonymousDivinationSessionsData
    } = StatisticsState.data.sessionData;
    
    if (!dateRange || !dateRange.length) {
      console.warn('日期范围数据不完整');
      return;
    }
    
    // 计算总计值
    let totalNewSessions = 0;
    let totalCompletedSessions = 0;
    let totalSingleNewline = 0;
    let totalDoubleNewline = 0;
    let totalParagraphSuccess = 0;
    let totalParagraphFail = 0;
    let totalDailyFortune = 0;
    let totalDivination = 0;

    // 匿名会话总计值
    let totalAnonymousSessions = 0;
    let totalAnonymousDailyFortune = 0;
    let totalAnonymousDivination = 0;
    
    const lastDate = dateRange[dateRange.length - 1];
    
    // 计算选中时间范围内的总数
    dateRange.forEach(date => {
      totalNewSessions += newSessionsData[date] || 0;
      totalCompletedSessions += completedSessionsData[date] || 0;
      totalSingleNewline += singleNewlineData[date] || 0;
      totalDoubleNewline += doubleNewlineData[date] || 0;
      totalParagraphSuccess += paragraphDetectionSuccessData[date] || 0;
      totalParagraphFail += paragraphDetectionFailData[date] || 0;
      totalDailyFortune += dailyFortuneSessionsData[date] || 0;
      totalDivination += divinationSessionsData[date] || 0;

      // 匿名会话数据累计
      totalAnonymousSessions += newAnonymousSessionsData[date] || 0;
      totalAnonymousDailyFortune += anonymousDailyFortuneSessionsData[date] || 0;
      totalAnonymousDivination += anonymousDivinationSessionsData[date] || 0;
    });
    
    // 更新UI显示 - 总会话数包含注册用户和匿名用户
    const { totalAnonymousSessionsData } = StatisticsState.data.sessionData;
    const totalRegisteredSessions = totalSessionsData[lastDate] || 0;
    const totalAnonymousSessionsCount = totalAnonymousSessionsData[lastDate] || 0;
    const grandTotalSessions = totalRegisteredSessions + totalAnonymousSessionsCount;

    document.getElementById('total-sessions').textContent = formatNumber(grandTotalSessions);
    document.getElementById('new-sessions').textContent = formatNumber(totalNewSessions);
    document.getElementById('completed-sessions').textContent = formatNumber(totalCompletedSessions);
    document.getElementById('single-newline').textContent = formatNumber(totalSingleNewline);
    document.getElementById('double-newline').textContent = formatNumber(totalDoubleNewline);
    document.getElementById('paragraph-success').textContent = formatNumber(totalParagraphSuccess);
    document.getElementById('paragraph-fail').textContent = formatNumber(totalParagraphFail);
    document.getElementById('daily-fortune-sessions').textContent = formatNumber(totalDailyFortune);
    document.getElementById('divination-sessions').textContent = formatNumber(totalDivination);

    // 更新匿名会话UI显示
    document.getElementById('anonymous-sessions').textContent = formatNumber(totalAnonymousSessions);
    document.getElementById('anonymous-daily-fortune-sessions').textContent = formatNumber(totalAnonymousDailyFortune);
    document.getElementById('anonymous-divination-sessions').textContent = formatNumber(totalAnonymousDivination);
  } catch (error) {
    console.error('更新会话统计摘要出错:', error);
    showError('更新会话统计摘要失败: ' + error.message);
  }
}

// HTML转义函数
function escapeHtml(text) {
  if (!text) return '';
  
  return text
    .replace(/&/g, "&amp;")
    .replace(/</g, "&lt;")
    .replace(/>/g, "&gt;")
    .replace(/"/g, "&quot;")
    .replace(/'/g, "&#039;");
}

// 会话明细状态对象
const SessionDetails = {
  currentPage: 1,
  totalPages: 1,
  pageSize: 20,
  data: [],
  searchQuery: '',
  sessionIdQuery: '',  // 新增sessionId查询
  statusFilter: '',
  lastDateRange: null,
  isLoading: false
};

// 将SessionDetails对象暴露为全局变量，以便其他模块访问
window.SessionDetails = SessionDetails;

// 获取会话明细数据
function fetchSessionDetails() {
  // 获取最新的日期范围
  const dateRange = $('#daterange').val().split(' - ');
  const startDate = dateRange[0];
  const endDate = dateRange[1];
  
  // 记录当前日期范围，用于检测变化
  if (!SessionDetails.lastDateRange || 
      SessionDetails.lastDateRange.startDate !== startDate || 
      SessionDetails.lastDateRange.endDate !== endDate) {
    console.log('日期范围已变更，重置分页状态');
    SessionDetails.currentPage = 1;
    SessionDetails.lastDateRange = { startDate, endDate };
  }
  
  // 确保currentPage是数字类型，并且是有效值
  let currentPage = parseInt(SessionDetails.currentPage) || 1;
  if (isNaN(currentPage) || currentPage < 1) {
    currentPage = 1;
    SessionDetails.currentPage = 1;
  }
  
  const pageSize = parseInt(SessionDetails.pageSize) || 20;
  // 计算正确的offset
  const offset = (currentPage - 1) * pageSize;
  
  console.log('分页参数(请求前):', {
    currentPage: currentPage,
    pageSize: pageSize,
    offset: offset,
    dateRange: [startDate, endDate]
  });
  
  // 显示加载状态
  $('#session-details-body').html('<tr><td colspan="5" class="loading">加载会话明细数据中...</td></tr>');
  
  // 构建查询参数
  const params = {
    startDate,
    endDate,
    limit: pageSize,
    offset: offset
  };
  
  // 如果有搜索关键词，添加到参数中
  if (SessionDetails.searchQuery) {
    params.query = SessionDetails.searchQuery;
  }
  
  // 如果有会话ID查询，添加到参数中
  if (SessionDetails.sessionIdQuery) {
    params.sessionId = SessionDetails.sessionIdQuery;
  }
  
  // 如果有状态筛选，添加到参数中
  if (SessionDetails.statusFilter) {
    params.status = SessionDetails.statusFilter;
  }
  
  console.log('发送请求参数:', params);
  
  // 防止重复请求
  if (SessionDetails.isLoading) {
    console.log('上一个请求尚未完成，忽略此次请求');
    return;
  }
  
  SessionDetails.isLoading = true;
  
  axios.get('/api/session-details', { params })
    .then(response => {
      SessionDetails.isLoading = false;
      
      if (response.data.success) {
        console.log('接收到会话明细数据:', response.data.data);
        SessionDetails.data = response.data.data.sessions;
        
        // 使用后端返回的分页信息
        const total = parseInt(response.data.data.total);
        const serverCurrentPage = parseInt(response.data.data.currentPage) || currentPage;
        const serverTotalPages = parseInt(response.data.data.totalPages) || Math.ceil(total / pageSize);
        
        console.log('服务器返回的分页信息:', {
          total: total,
          serverCurrentPage: serverCurrentPage,
          serverTotalPages: serverTotalPages,
          currentPageBeforeUpdate: SessionDetails.currentPage
        });
        
        // 更新当前页和总页数，但确保只增加/减少一页
        if (serverCurrentPage !== currentPage) {
          console.log(`服务器返回的页码(${serverCurrentPage})与请求页码(${currentPage})不一致，使用服务器页码`);
        }
        
        // 更新页码，但确保变化不超过1页
        SessionDetails.currentPage = serverCurrentPage;
        SessionDetails.totalPages = serverTotalPages;
        
        console.log('更新后的分页信息:', {
          total: total,
          pageSize: pageSize,
          totalPages: SessionDetails.totalPages,
          currentPage: SessionDetails.currentPage
        });
        
        // 更新分页信息
        updatePagination();
        
        // 渲染会话明细
        renderSessionDetails();
        
        // 滚动到表格顶部
        scrollToTableTop();
      } else {
        $('#session-details-body').html(`<tr><td colspan="5" class="error">获取数据失败: ${response.data.message}</td></tr>`);
      }
    })
    .catch(error => {
      SessionDetails.isLoading = false;
      console.error('获取会话明细数据失败:', error);
      $('#session-details-body').html(`<tr><td colspan="5" class="error">获取数据失败: ${error.message}</td></tr>`);
    });
}

// 滚动到表格顶部
function scrollToTableTop() {
  const tableTop = $('.session-details-table-container').offset().top - 20;
  $('html, body').animate({
    scrollTop: tableTop
  }, 100);
}

// 渲染会话明细
function renderSessionDetails() {
  if (!SessionDetails.data || SessionDetails.data.length === 0) {
    $('#session-details-body').html('<tr><td colspan="5" class="no-data">暂无会话数据</td></tr>');
    return;
  }
  
  try {
    const tableBody = $('#session-details-body');
    tableBody.empty();
    
    SessionDetails.data.forEach(session => {
      const row = $('<tr></tr>');
      
      // 添加时间列
      const timestamp = new Date(session.timestamp);
      const formattedDate = timestamp.toLocaleDateString('zh-CN');
      const formattedTime = timestamp.toLocaleTimeString('zh-CN');
      row.append(`<td>${formattedDate} ${formattedTime}</td>`);
      
      // 添加会话ID列
      row.append(`<td>${session.id}</td>`);
      
      // 添加问题内容列
      const questionText = escapeHtml(session.question || '');
      const truncatedQuestion = questionText.length > 50 ? questionText.substring(0, 50) + '...' : questionText;
      row.append(`<td class="question-cell"><div class="question-content">${truncatedQuestion}</div></td>`);
      
      // 添加状态列
      let statusText = '';
      let statusClass = '';
      
      if (session.is_security === 1) {
        statusText = '安全检测';
        statusClass = 'status-security';
      } else if (session.is_potential_security === 1) {
        statusText = '潜在安全';
        statusClass = 'status-potential-security';
      } else if (session.status === 'pending') {
        statusText = '待处理';
        statusClass = 'status-pending';
      } else if (session.status === 'completed') {
        statusText = '已完成';
        statusClass = 'status-completed';
      } else {
        statusText = session.status || '未知';
        statusClass = 'status-unknown';
      }
      
      // 如果是占卜会话，添加是非占卜标识
      if (session.is_divination === 1) {
        statusText += ' (是非占卜)';
      }
      
      row.append(`<td><span class="status-badge ${statusClass}">${statusText}</span></td>`);
      
      // 添加点击事件
      row.on('click', function() {
        openSessionDetail(session.id);
      });
      
      tableBody.append(row);
    });
  } catch (error) {
    console.error('渲染会话明细出错:', error);
    showError('渲染会话明细失败: ' + error.message);
  }
}

// 打开会话详情模态框
function openSessionDetail(sessionId) {
  // 重置模态框内容
  resetSessionDetailModal();
  
  // 显示模态框
  $('#session-detail-modal').css('display', 'block');
  
  // 获取会话详情数据
  fetchSessionDetail(sessionId);
}

// 重置会话详情模态框
function resetSessionDetailModal() {
  $('#detail-question').text('');
  $('#detail-timestamp').text('');
  $('#detail-status').text('');
  
  // 重置安全检测信息
  $('#security-info-container').hide();
  $('#ethical-category').text('');
  $('#ethical-reason').text('');
  $('#ethical-confidence').text('');
  
  // 重置全局变量
  followupEthicalCategories = null;
  followupEthicalReasons = null;
  
  $('#reading-result-content').html('<div class="loading">加载中...</div>');
  $('#deep-analysis-content').html('<div class="loading">加载中...</div>');
  $('#dialog-history-content').html('<div class="loading">加载中...</div>');
  
  // 重置标签页状态
  $('.detail-tab').removeClass('active');
  $('.detail-tab[data-tab="reading-result"]').addClass('active');
  
  $('.tab-content').removeClass('active');
  $('#reading-result-content').addClass('active');
}

// 全局变量，用于存储追问检测信息
let followupEthicalCategories = null;
let followupEthicalReasons = null;

// 获取会话详情数据
function fetchSessionDetail(sessionId) {
  
  axios.get(`/api/session-detail/${sessionId}`)
    .then(response => {
      console.log('会话详情API响应:', response.data);
      
      if (response.data.success) {
        const session = response.data.data;
        console.log('会话详情数据:', session);
        console.log('dialog_history类型:', typeof session.dialog_history);
        console.log('dialog_history值:', session.dialog_history);
        
        renderSessionDetail(session);
      } else {
        showError('获取会话详情失败: ' + response.data.message);
      }
    })
    .catch(error => {
      console.error('获取会话详情失败:', error);
      showError('获取会话详情失败: ' + error.message);
    });
}

// 渲染会话详情
function renderSessionDetail(session) {
  // 渲染基本信息
  $('#detail-question').text(session.question);
  $('#detail-timestamp').text(moment(session.timestamp).format('YYYY-MM-DD HH:mm:ss'));
  
  // 状态翻译
  let statusText = session.status;
  if (session.ethical_status === 'ethical_intervention' || 
      session.ethical_status === 'ethical_intervention_follow') {
    statusText = '安全检测';
  } else if (session.ethical_status === 'potential_ethical_issue' ||
             session.ethical_status === 'potential_ethical_issue_follow') {
    statusText = '潜在安全检测';
  } else if (session.status === 'pending') {
    statusText = '待处理';
  } else if (session.status === 'completed') {
    statusText = '已完成';
  }
  $('#detail-status').text(statusText);
  
  // 更新模态框标题
  let modalTitle = '会话详情';
  if (session.ethical_status === 'ethical_intervention') {
    modalTitle = '安全检测会话详情';
  } else if (session.ethical_status === 'ethical_intervention_follow') {
    modalTitle = '安全检测追问会话详情';
  } else if (session.ethical_status === 'potential_ethical_issue') {
    modalTitle = '潜在安全检测会话详情';
  } else if (session.ethical_status === 'potential_ethical_issue_follow') {
    modalTitle = '潜在安全检测追问会话详情';
  }
  $('#detail-modal-title').text(modalTitle);
  
  // 检查是否是安全检测相关会话
  const isSecurityIssue = session.ethical_status === 'ethical_intervention' || 
                         session.ethical_status === 'ethical_intervention_follow' || 
                         session.ethical_status === 'potential_ethical_issue' || 
                         session.ethical_status === 'potential_ethical_issue_follow';
  
  // 保存追问检测信息到全局变量，用于在追问历史中显示
  try {
    if (session.followup_ethical_categories) {
      if (typeof session.followup_ethical_categories === 'string') {
        followupEthicalCategories = JSON.parse(session.followup_ethical_categories);
        // 处理嵌套JSON
        if (typeof followupEthicalCategories === 'string') {
          followupEthicalCategories = JSON.parse(followupEthicalCategories);
        }
      } else {
        followupEthicalCategories = session.followup_ethical_categories;
      }
    } else {
      followupEthicalCategories = null;
    }

    if (session.followup_ethical_reasons) {
      if (typeof session.followup_ethical_reasons === 'string') {
        followupEthicalReasons = JSON.parse(session.followup_ethical_reasons);
        // 处理嵌套JSON
        if (typeof followupEthicalReasons === 'string') {
          followupEthicalReasons = JSON.parse(followupEthicalReasons);
        }
      } else {
        followupEthicalReasons = session.followup_ethical_reasons;
      }
    } else {
      followupEthicalReasons = null;
    }
    
    console.log('解析后的追问检测类别:', followupEthicalCategories);
    console.log('解析后的追问检测原因:', followupEthicalReasons);
  } catch (error) {
    console.error('解析追问检测信息失败:', error);
    followupEthicalCategories = null;
    followupEthicalReasons = null;
  }
  
  // 处理安全检测信息
  if (isSecurityIssue) {
    // 显示安全检测信息区域
    $('#security-info-container').show();
    
    // 根据会话状态添加不同的样式类
    $('#security-info-container').removeClass('ethical-intervention potential-ethical-issue ethical-intervention-follow potential-ethical-issue-follow');
    $('#security-info-container').addClass(session.ethical_status);
    
    // 基础会话安全检测信息 (ethical_intervention 和 potential_ethical_issue)
    const isBaseSecuritySession = session.ethical_status === 'ethical_intervention' || 
                                 session.ethical_status === 'potential_ethical_issue';
    
    // 显示基础检测类型
    if (session.Ethical_category) {
      $('#ethical-category').text(session.Ethical_category);
    } else {
      $('#ethical-category').html('<span class="no-data">无</span>');
    }
    
    // 显示基础检测原因
    if (session.Ethical_reason) {
      $('#ethical-reason').text(session.Ethical_reason);
    } else {
      $('#ethical-reason').html('<span class="no-data">无</span>');
    }
    
    // 显示基础检测置信度
    if (session.Ethical_confidence) {
      // 将置信度格式化为百分比
      const confidencePercent = (parseFloat(session.Ethical_confidence) * 100).toFixed(2) + '%';
      $('#ethical-confidence').text(confidencePercent);
    } else {
      $('#ethical-confidence').html('<span class="no-data">无</span>');
    }
  }
  
  // 渲染塔罗解读
  if (session.reading_result) {
    renderReadingResult(session.reading_result);
  } else {
    $('#reading-result-content').html('<div class="no-data">暂无塔罗解读数据</div>');
  }
  
  // 渲染深度分析
  if (session.deep_analysis) {
    renderDeepAnalysis(session.deep_analysis);
  } else {
    $('#deep-analysis-content').html('<div class="no-data">暂无深度分析数据</div>');
  }
  
  // 渲染追问历史
  if (session.dialog_history) {
    renderDialogHistory(session.dialog_history);
  } else {
    $('#dialog-history-content').html('<div class="no-data">暂无追问历史数据</div>');
  }
}

// 渲染塔罗解读
function renderReadingResult(readingResult) {
  console.log('开始渲染塔罗解读，原始数据类型:', typeof readingResult);
  
  if (!readingResult) {
    console.log('塔罗解读数据为空');
    $('#reading-result-content').html('<div class="no-data">暂无塔罗解读数据</div>');
    return;
  }
  
  try {
    let resultObj;
    if (typeof readingResult === 'string') {
      console.log('塔罗解读是字符串类型，尝试解析JSON');
      resultObj = JSON.parse(readingResult);
    } else {
      console.log('塔罗解读是对象类型，无需解析');
      resultObj = readingResult;
    }
    
    console.log('塔罗解读数据解析后:', resultObj);
    console.log('塔罗解读数据结构:', Object.keys(resultObj));
    
    let html = '<div class="reading-result-container">';
    
    // 如果有content字段，直接显示内容
    if (resultObj.content) {
      console.log('塔罗解读有content字段，值类型:', typeof resultObj.content);
      // 处理双换行符为段落
      const paragraphs = resultObj.content.split('\n\n');
      console.log(`塔罗解读content拆分为${paragraphs.length}个段落`);
      
      paragraphs.forEach((paragraph, index) => {
        if (paragraph.trim()) {
          // 先进行HTML转义，然后再替换换行符为<br>
          const escapedParagraph = escapeHtml(paragraph);
          const formattedParagraph = escapedParagraph.replace(/\n/g, '<br>');
          html += `<div class="reading-paragraph">${formattedParagraph}</div>`;
          console.log(`添加塔罗解读段落${index + 1}, 长度: ${paragraph.length}`);
        }
      });
    } 
    // 否则，格式化显示整个对象
    else {
      console.log('塔罗解读没有content字段，遍历对象属性');
      // 遍历对象的所有属性
      for (const key in resultObj) {
        if (resultObj.hasOwnProperty(key)) {
          const value = resultObj[key];
          console.log(`处理塔罗解读属性: ${key}, 类型: ${typeof value}`);
          
          // 如果值是字符串，直接显示
          if (typeof value === 'string') {
            // 处理双换行符为段落
            const paragraphs = value.split('\n\n');
            html += `<div class="reading-section">
              <div class="reading-title">${escapeHtml(key)}</div>`;
            
            paragraphs.forEach((paragraph, index) => {
              if (paragraph.trim()) {
                // 先进行HTML转义，然后再替换换行符为<br>
                const escapedParagraph = escapeHtml(paragraph);
                const formattedParagraph = escapedParagraph.replace(/\n/g, '<br>');
                html += `<div class="reading-paragraph">${formattedParagraph}</div>`;
                console.log(`添加塔罗解读属性${key}的段落${index + 1}, 长度: ${paragraph.length}`);
              }
            });
            
            html += '</div>';
          } 
          // 如果值是对象或数组，递归处理
          else if (typeof value === 'object' && value !== null) {
            html += `<div class="reading-section">
              <div class="reading-title">${escapeHtml(key)}</div>
              <div class="reading-content">${escapeHtml(JSON.stringify(value, null, 2))}</div>
            </div>`;
            console.log(`添加塔罗解读对象属性: ${key}`);
          }
        }
      }
    }
    
    html += '</div>';
    console.log('塔罗解读HTML生成完成，长度:', html.length);
    $('#reading-result-content').html(html);
  } catch (error) {
    console.error('解析塔罗解读数据失败:', error);
    $('#reading-result-content').html('<div class="error">解析塔罗解读数据失败: ' + error.message + '</div>');
  }
}

// 渲染深度分析
function renderDeepAnalysis(deepAnalysis) {
  console.log('开始渲染深度分析，原始数据类型:', typeof deepAnalysis);
  
  if (!deepAnalysis) {
    console.log('深度分析数据为空');
    $('#deep-analysis-content').html('<div class="no-data">暂无深度分析数据</div>');
    return;
  }
  
  try {
    console.log('深度分析数据:', deepAnalysis);
    console.log('深度分析数据长度:', deepAnalysis.length);
    
    // 尝试检测是否为JSON字符串
    let isJson = false;
    let jsonData = null;
    
    if (typeof deepAnalysis === 'string' && 
        (deepAnalysis.trim().startsWith('{') || deepAnalysis.trim().startsWith('['))) {
      try {
        jsonData = JSON.parse(deepAnalysis);
        isJson = true;
        console.log('深度分析似乎是JSON字符串，已解析为:', jsonData);
      } catch (e) {
        console.log('深度分析不是有效的JSON字符串');
        isJson = false;
      }
    }
    
    let html = '<div class="reading-result-container">';
    
    // 如果是JSON对象，使用与塔罗解读相同的处理方式
    if (isJson && jsonData) {
      console.log('以JSON对象方式处理深度分析');
      
      if (jsonData.content) {
        console.log('深度分析JSON有content字段');
        const paragraphs = jsonData.content.split('\n\n');
        console.log(`深度分析content拆分为${paragraphs.length}个段落`);
        
        paragraphs.forEach((paragraph, index) => {
          if (paragraph.trim()) {
            const escapedParagraph = escapeHtml(paragraph);
            const formattedParagraph = escapedParagraph.replace(/\n/g, '<br>');
            html += `<div class="reading-paragraph">${formattedParagraph}</div>`;
            console.log(`添加深度分析段落${index + 1}, 长度: ${paragraph.length}`);
          }
        });
      } else {
        console.log('深度分析JSON没有content字段，遍历对象属性');
        for (const key in jsonData) {
          if (jsonData.hasOwnProperty(key)) {
            const value = jsonData[key];
            console.log(`处理深度分析属性: ${key}, 类型: ${typeof value}`);
            
            if (typeof value === 'string') {
              const paragraphs = value.split('\n\n');
              html += `<div class="reading-section">
                <div class="reading-title">${escapeHtml(key)}</div>`;
              
              paragraphs.forEach((paragraph, index) => {
                if (paragraph.trim()) {
                  const escapedParagraph = escapeHtml(paragraph);
                  const formattedParagraph = escapedParagraph.replace(/\n/g, '<br>');
                  html += `<div class="reading-paragraph">${formattedParagraph}</div>`;
                  console.log(`添加深度分析属性${key}的段落${index + 1}, 长度: ${paragraph.length}`);
                }
              });
              
              html += '</div>';
            } else if (typeof value === 'object' && value !== null) {
              html += `<div class="reading-section">
                <div class="reading-title">${escapeHtml(key)}</div>
                <div class="reading-content">${escapeHtml(JSON.stringify(value, null, 2))}</div>
              </div>`;
              console.log(`添加深度分析对象属性: ${key}`);
            }
          }
        }
      }
    }
    // 否则按纯文本处理
    else {
      console.log('以纯文本方式处理深度分析');
      // 处理双换行符为段落
      const paragraphs = deepAnalysis.split('\n\n');
      console.log(`深度分析文本拆分为${paragraphs.length}个段落`);
      
      paragraphs.forEach((paragraph, index) => {
        if (paragraph.trim()) {
          // 先进行HTML转义，然后再替换换行符为<br>
          const escapedParagraph = escapeHtml(paragraph);
          const formattedParagraph = escapedParagraph.replace(/\n/g, '<br>');
          html += `<div class="reading-paragraph">${formattedParagraph}</div>`;
          console.log(`添加深度分析段落${index + 1}, 长度: ${paragraph.length}`);
        }
      });
    }
    
    html += '</div>';
    console.log('深度分析HTML生成完成，长度:', html.length);
    $('#deep-analysis-content').html(html);
  } catch (error) {
    console.error('解析深度分析数据失败:', error);
    $('#deep-analysis-content').html('<div class="error">解析深度分析数据失败: ' + error.message + '</div>');
  }
}

// 渲染追问历史
function renderDialogHistory(dialogHistory) {
  console.log('开始渲染追问历史，原始数据:', dialogHistory);
  
  if (!dialogHistory) {
    console.log('追问历史数据为空');
    $('#dialog-history-content').html('<div class="no-data">暂无追问历史数据</div>');
    return;
  }
  
  try {
    let dialogObj;
    if (typeof dialogHistory === 'string') {
      console.log('追问历史是字符串类型，尝试解析JSON');
      dialogObj = JSON.parse(dialogHistory);
    } else {
      console.log('追问历史是对象类型，无需解析');
      dialogObj = dialogHistory;
    }
    
    console.log('解析后的追问历史数据:', dialogObj);
    
    if (!Array.isArray(dialogObj) || dialogObj.length === 0) {
      console.log('追问历史不是数组或为空数组');
      $('#dialog-history-content').html('<div class="no-data">暂无追问历史数据</div>');
      return;
    }
    
    let html = '';
    console.log(`开始处理${dialogObj.length}条对话记录`);
    
    // 计算用户消息的索引，用于匹配安全检测信息
    let userMessageIndex = 0;
    
    dialogObj.forEach((dialog, index) => {
      console.log(`处理第${index + 1}条对话:`, dialog);
      
      // 获取角色信息，支持role和type两种字段
      const role = dialog.role || dialog.type;
      
      if (role === 'user') {
        // 为用户消息添加安全检测信息
        let securityInfoHtml = '';
        
        // 使用userMessageIndex作为标识，检查是否有对应的安全检测信息
        if (followupEthicalCategories || followupEthicalReasons) {
          // 如果有对应的安全检测类别
          let categoryInfo = '';
          if (followupEthicalCategories) {
            let category = null;
            
            // 尝试根据用户消息索引获取检测类别
            if (Array.isArray(followupEthicalCategories)) {
              if (userMessageIndex < followupEthicalCategories.length) {
                category = followupEthicalCategories[userMessageIndex];
              }
            } else if (typeof followupEthicalCategories === 'object') {
              // 如果是对象，尝试获取索引属性
              category = followupEthicalCategories[userMessageIndex] || 
                         followupEthicalCategories[String(userMessageIndex)] ||
                         followupEthicalCategories['index_' + userMessageIndex];
            }
            
            if (category) {
              if (typeof category === 'object') {
                for (const key in category) {
                  if (category.hasOwnProperty(key)) {
                    const value = category[key];
                    // 如果值是数字且在0-1之间，可能是置信度，转换为百分比
                    const displayValue = (!isNaN(value) && value >= 0 && value <= 1) 
                      ? (value * 100).toFixed(2) + '%' 
                      : String(value);
                    categoryInfo += `<span class="security-tag">${escapeHtml(key)}: ${escapeHtml(displayValue)}</span> `;
                  }
                }
              } else if (category) {
                categoryInfo += `<span class="security-tag">${escapeHtml(String(category))}</span>`;
              }
            }
          }
          
          // 如果有对应的安全检测原因
          let reasonInfo = '';
          if (followupEthicalReasons) {
            let reason = null;
            
            // 尝试根据用户消息索引获取检测原因
            if (Array.isArray(followupEthicalReasons)) {
              if (userMessageIndex < followupEthicalReasons.length) {
                reason = followupEthicalReasons[userMessageIndex];
              }
            } else if (typeof followupEthicalReasons === 'object') {
              // 如果是对象，尝试获取索引属性
              reason = followupEthicalReasons[userMessageIndex] || 
                       followupEthicalReasons[String(userMessageIndex)] ||
                       followupEthicalReasons['index_' + userMessageIndex];
            }
            
            if (reason) {
              if (typeof reason === 'object') {
                for (const key in reason) {
                  if (reason.hasOwnProperty(key)) {
                    reasonInfo += `<div class="security-reason"><strong>${escapeHtml(key)}:</strong> ${escapeHtml(String(reason[key]))}</div>`;
                  }
                }
              } else if (reason) {
                reasonInfo += `<div class="security-reason">${escapeHtml(String(reason))}</div>`;
              }
            }
          }
          
          // 只有当有安全检测信息时才显示
          if (categoryInfo || reasonInfo) {
            securityInfoHtml = `
              <div class="message-security-info">
                ${categoryInfo ? `<div class="security-label">检测类型</div><div class="security-value">${categoryInfo}</div>` : ''}
                ${reasonInfo ? `<div class="security-label">检测原因</div><div class="security-value">${reasonInfo}</div>` : ''}
              </div>
            `;
          }
        }
        
        html += `
          <div class="dialog-item user-message">
            <div class="message-content">${escapeHtml(dialog.content)}</div>
            ${securityInfoHtml}
          </div>
        `;
        
        // 增加用户消息计数
        userMessageIndex++;
      } else if (role === 'assistant') {
        html += `
          <div class="dialog-item assistant-message">
            <div class="message-content">${escapeHtml(dialog.content)}</div>
          </div>
        `;
      } else {
        console.log(`未知的对话角色: ${role}`);
      }
    });
    
    console.log('生成的HTML:', html);
    $('#dialog-history-content').html(html || '<div class="no-data">暂无追问历史数据</div>');
  } catch (error) {
    console.error('解析追问历史数据失败:', error);
    $('#dialog-history-content').html('<div class="error">解析追问历史数据失败: ' + error.message + '</div>');
  }
}

// 更新分页信息
function updatePagination() {
  // 确保使用整数值
  const currentPage = parseInt(SessionDetails.currentPage) || 1;
  const totalPages = parseInt(SessionDetails.totalPages) || 1;
  
  // 防止页码超出范围
  if (currentPage < 1 || currentPage > totalPages) {
    console.warn('页码超出范围，重置为第1页');
    SessionDetails.currentPage = 1;
  }
  
  $('#page-info').text(`第 ${currentPage} 页 / 共 ${totalPages} 页`);
  
  // 更新翻页按钮状态
  const prevDisabled = currentPage <= 1;
  const nextDisabled = currentPage >= totalPages;
  
  $('#prev-page').prop('disabled', prevDisabled);
  $('#next-page').prop('disabled', nextDisabled);
  
  console.log('更新分页显示:', { 
    currentPage, 
    totalPages, 
    prevDisabled, 
    nextDisabled 
  });
}

// 初始化会话明细功能
function initSessionDetails() {
  // 获取初始数据
  fetchSessionDetails();
  
  // 绑定搜索按钮点击事件
  $('#session-search-btn').on('click', function() {
    SessionDetails.searchQuery = $('#session-search').val().trim();
    SessionDetails.sessionIdQuery = $('#session-id-search').val().trim();
    SessionDetails.currentPage = 1;
    fetchSessionDetails();
  });
  
  // 绑定搜索框回车事件
  $('#session-search').on('keypress', function(e) {
    if (e.which === 13) {
      SessionDetails.searchQuery = $(this).val().trim();
      SessionDetails.sessionIdQuery = $('#session-id-search').val().trim();
      SessionDetails.currentPage = 1;
      fetchSessionDetails();
    }
  });
  
  // 绑定会话ID搜索框回车事件
  $('#session-id-search').on('keypress', function(e) {
    if (e.which === 13) {
      SessionDetails.searchQuery = $('#session-search').val().trim();
      SessionDetails.sessionIdQuery = $(this).val().trim();
      SessionDetails.currentPage = 1;
      fetchSessionDetails();
    }
  });
  
  // 绑定状态筛选事件
  $('#status-filter').on('change', function() {
    SessionDetails.statusFilter = $(this).val();
    SessionDetails.currentPage = 1;
    fetchSessionDetails();
  });
  
  // 绑定翻页事件
  $('#prev-page').on('click', function() {
    if (parseInt(SessionDetails.currentPage) > 1) {
      // 强制转换为数字并确保只减1
      const oldPage = parseInt(SessionDetails.currentPage);
      const newPage = oldPage - 1;
      
      // 确保页码在有效范围内
      SessionDetails.currentPage = Math.max(1, newPage);
      
      console.log(`点击上一页，页码从 ${oldPage} 变为 ${SessionDetails.currentPage}`);
      
      // 防止重复点击
      $(this).prop('disabled', true);
      $('#next-page').prop('disabled', true);
      
      fetchSessionDetails();
      
      // 1秒后恢复按钮状态，防止快速点击
      setTimeout(() => {
        updatePagination();
      }, 500);
    }
  });
  
  $('#next-page').on('click', function() {
    if (parseInt(SessionDetails.currentPage) < parseInt(SessionDetails.totalPages)) {
      // 强制转换为数字并确保只加1
      const oldPage = parseInt(SessionDetails.currentPage);
      const newPage = oldPage + 1;
      
      // 确保页码不超过总页数
      SessionDetails.currentPage = Math.min(newPage, parseInt(SessionDetails.totalPages));
      
      console.log(`点击下一页，页码从 ${oldPage} 变为 ${SessionDetails.currentPage}`);
      
      // 防止重复点击
      $(this).prop('disabled', true);
      $('#prev-page').prop('disabled', true);
      
      fetchSessionDetails();
      
      // 1秒后恢复按钮状态，防止快速点击
      setTimeout(() => {
        updatePagination();
      }, 500);
    }
  });
  
  // 绑定模态框关闭事件
  $('.close-modal').on('click', function() {
    $('#session-detail-modal').css('display', 'none');
  });
  
  // 点击模态框外部关闭模态框
  $(window).on('click', function(event) {
    if (event.target.id === 'session-detail-modal') {
      $('#session-detail-modal').css('display', 'none');
    }
  });
  
  // 绑定详情标签页切换事件
  $('.detail-tab').on('click', function() {
    const tabId = $(this).data('tab');
    
    // 切换标签页激活状态
    $('.detail-tab').removeClass('active');
    $(this).addClass('active');
    
    // 切换内容显示
    $('.tab-content').removeClass('active');
    $(`#${tabId}-content`).addClass('active');
  });
} 